# Fast2SMS Integration Setup

This application now uses Fast2SMS for OTP delivery instead of <PERSON><PERSON><PERSON>. Follow these steps to configure Fast2SMS:

## 1. Create Fast2SMS Account

1. Visit [Fast2SMS](https://www.fast2sms.com/)
2. Sign up for a new account
3. Complete the verification process
4. Add credits to your account

## 2. Get API Key

1. Login to your Fast2SMS dashboard
2. Go to **API** section
3. Copy your **API Key**

## 3. Configure Application

Update your `application.yml` or environment variables:

```yaml
# Fast2SMS Configuration (for OTP)
fast2sms:
  api:
    key: YOUR_ACTUAL_API_KEY_HERE  # Replace with your Fast2SMS API key
  sender:
    id: FSTSMS                     # Default sender ID (can be customized)
  base:
    url: https://www.fast2sms.com/dev/bulkV2
```

## 4. Environment Variables (Recommended for Production)

Instead of hardcoding in `application.yml`, use environment variables:

```bash
export FAST2SMS_API_KEY=your_actual_api_key_here
export FAST2SMS_SENDER_ID=FSTSMS
export FAST2SMS_BASE_URL=https://www.fast2sms.com/dev/bulkV2
```

Then update `application.yml`:

```yaml
fast2sms:
  api:
    key: ${FAST2SMS_API_KEY:your-fast2sms-api-key}
  sender:
    id: ${FAST2SMS_SENDER_ID:FSTSMS}
  base:
    url: ${FAST2SMS_BASE_URL:https://www.fast2sms.com/dev/bulkV2}
```

## 5. OTP Template Setup (Optional but Recommended)

For better delivery rates, create an OTP template in Fast2SMS:

1. Go to **Templates** in Fast2SMS dashboard
2. Create a new template for OTP
3. Use variables like: `Your CloudStore verification code is {#var#}. This code will expire in 5 minutes.`
4. Get template approval from Fast2SMS

## 6. Testing

### Development Mode
- If API key is not configured or set to `your-fast2sms-api-key`, OTPs will be logged to console
- Check application logs for OTP codes during testing

### Production Mode
- Ensure valid API key is configured
- Monitor Fast2SMS dashboard for delivery reports
- Check application logs for any errors

## 7. API Endpoints

The following endpoints use Fast2SMS for OTP:

- `POST /api/auth/register` - Send OTP during registration
- `POST /api/auth/login` - Send OTP for login
- `POST /api/auth/verify-otp` - Verify OTP

## 8. Phone Number Format

The application automatically formats phone numbers:
- Accepts: `**********`, `+************`, `91-**********`
- Converts to: `+************` for storage
- Sends to Fast2SMS as: `**********` (without country code)

## 9. Error Handling

Common issues and solutions:

### API Key Issues
- **Error**: "Invalid API key"
- **Solution**: Verify API key in Fast2SMS dashboard

### Insufficient Balance
- **Error**: "Insufficient balance"
- **Solution**: Add credits to Fast2SMS account

### Invalid Phone Number
- **Error**: "Invalid mobile number"
- **Solution**: Ensure phone number is valid Indian mobile number

### Rate Limiting
- **Error**: "Rate limit exceeded"
- **Solution**: Implement retry logic or upgrade Fast2SMS plan

## 10. Monitoring

Monitor the following:
- Application logs for OTP sending status
- Fast2SMS dashboard for delivery reports
- User feedback for OTP delivery issues

## 11. Security Best Practices

1. **Never commit API keys** to version control
2. **Use environment variables** for sensitive configuration
3. **Implement rate limiting** to prevent OTP abuse
4. **Set appropriate OTP expiry** (currently 5 minutes)
5. **Log security events** for monitoring

## 12. Cost Optimization

- **Use templates** for better rates
- **Monitor usage** in Fast2SMS dashboard
- **Implement caching** to avoid duplicate OTPs
- **Set up alerts** for high usage

## Support

For Fast2SMS specific issues:
- Visit [Fast2SMS Support](https://www.fast2sms.com/contact)
- Check [Fast2SMS Documentation](https://www.fast2sms.com/docs)

For application issues:
- Check application logs
- Verify configuration
- Test with development mode first
