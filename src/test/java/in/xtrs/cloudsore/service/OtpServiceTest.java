package in.xtrs.cloudsore.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@TestPropertySource(properties = {
    "fast2sms.api.key=test-api-key",
    "fast2sms.sender.id=FSTSMS",
    "fast2sms.base.url=https://www.fast2sms.com/dev/bulkV2"
})
class OtpServiceTest {

    @Autowired
    private OtpService otpService;

    @Test
    void testGenerateOtp() {
        String otp = otpService.generateOtp();
        
        assertNotNull(otp);
        assertEquals(6, otp.length());
        assertTrue(otp.matches("\\d{6}"));
    }

    @Test
    void testFormatPhoneNumber() {
        // Test various phone number formats
        assertEquals("+919876543210", otpService.formatPhoneNumber("9876543210"));
        assertEquals("+919876543210", otpService.formatPhoneNumber("+919876543210"));
        assertEquals("+919876543210", otpService.formatPhoneNumber("91-9876543210"));
        assertEquals("+919876543210", otpService.formatPhoneNumber("91 9876543210"));
    }

    @Test
    void testOtpExpiry() {
        var expiry = otpService.getOtpExpiry();
        assertNotNull(expiry);
        assertTrue(expiry.isAfter(java.time.LocalDateTime.now()));
    }

    @Test
    void testOtpValidation() {
        String otp = "123456";
        var expiry = java.time.LocalDateTime.now().plusMinutes(5);
        
        // Valid OTP
        assertTrue(otpService.isOtpValid(otp, otp, expiry));
        
        // Invalid OTP
        assertFalse(otpService.isOtpValid("654321", otp, expiry));
        
        // Expired OTP
        var pastExpiry = java.time.LocalDateTime.now().minusMinutes(1);
        assertFalse(otpService.isOtpValid(otp, otp, pastExpiry));
        
        // Null values
        assertFalse(otpService.isOtpValid(otp, null, expiry));
        assertFalse(otpService.isOtpValid(otp, otp, null));
    }

    @Test
    void testSendOtpInDevelopmentMode() {
        // In test mode with test API key, should return true but not actually send SMS
        boolean result = otpService.sendOtp("+919876543210", "123456");
        assertTrue(result);
    }
}
