package in.xtrs.cloudsore.entity;

import in.xtrs.cloudsore.enums.OrderStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;

@Entity
@Table(name = "delivery_assignments")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryAssignment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotNull(message = "Order is required")
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "order_id", nullable = false)
    private Order order;
    
    @NotNull(message = "Delivery manager is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "delivery_manager_id", nullable = false)
    private User deliveryManager;
    
    @Column(name = "assigned_at")
    private LocalDateTime assignedAt;
    
    @Column(name = "picked_up_at")
    private LocalDateTime pickedUpAt;
    
    @Column(name = "delivered_at")
    private LocalDateTime deliveredAt;
    
    @Column(name = "delivery_notes", columnDefinition = "TEXT")
    private String deliveryNotes;
    
    @Column(name = "customer_feedback", columnDefinition = "TEXT")
    private String customerFeedback;
    
    @Column(name = "delivery_rating")
    private Integer deliveryRating; // 1-5 stars
    
    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    public DeliveryAssignment(Order order, User deliveryManager) {
        this.order = order;
        this.deliveryManager = deliveryManager;
        this.assignedAt = LocalDateTime.now();
    }
    
    public void markAsPickedUp() {
        this.pickedUpAt = LocalDateTime.now();
    }
    
    public void markAsDelivered() {
        this.deliveredAt = LocalDateTime.now();
    }
}
