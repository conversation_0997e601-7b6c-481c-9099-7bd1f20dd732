package in.xtrs.cloudsore.enums;

/**
 * Tax types available in the system
 */
public enum TaxType {
    GST("GST", "Goods and Services Tax"),
    VAT("VAT", "Value Added Tax"),
    SERVICE_TAX("SERVICE_TAX", "Service Tax"),
    EXCISE_DUTY("EXCISE_DUTY", "Excise Duty"),
    CUSTOMS_DUTY("CUSTOMS_DUTY", "Customs Duty"),
    CESS("CESS", "Cess"),
    SURCHARGE("SURCHARGE", "Surcharge"),
    NONE("NONE", "No Tax");

    private final String code;
    private final String description;

    TaxType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return code;
    }
}
