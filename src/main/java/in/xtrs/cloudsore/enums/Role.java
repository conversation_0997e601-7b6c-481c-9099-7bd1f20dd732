package in.xtrs.cloudsore.enums;

import lombok.Getter;

/**
 * User roles in the system
 */
@Getter
public enum Role {
    SUPERADMIN("SUPERADMIN"),
    STORE_MANAGER("STORE_MANAGER"),
    DELIVERY_MANAGER("DELIVERY_MANAGER"),
    USER("USER"),
    GUEST("GUEST");

    private final String value;

    Role(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return value;
    }
}
