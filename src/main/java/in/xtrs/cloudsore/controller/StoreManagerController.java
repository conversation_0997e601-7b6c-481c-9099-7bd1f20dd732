package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.*;
import in.xtrs.cloudsore.entity.*;
import in.xtrs.cloudsore.enums.OrderStatus;
import in.xtrs.cloudsore.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/store-manager")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "Store Manager", description = "APIs for store manager operations")
@SecurityRequirement(name = "Bearer Authentication")
@PreAuthorize("hasAnyRole('STORE_MANAGER', 'SUPERADMIN')")
public class StoreManagerController {

    private final StoreService storeService;
    private final OrderService orderService;
    private final ProductService productService;
    private final AuthService authService;
    private final UserService userService;

    @Operation(
        summary = "Get my store",
        description = "Get the store managed by the current store manager"
    )
    @GetMapping("/my-store")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Store>> getMyStore(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);

        in.xtrs.cloudsore.dto.ApiResponse<Store> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(store, "Store retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get store orders",
        description = "Get all orders for the store managed by the current store manager"
    )
    @GetMapping("/orders")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Order>>> getStoreOrders(
            @RequestParam(required = false) OrderStatus status,
            @RequestParam(required = false, defaultValue = "0") int page,
            @RequestParam(required = false, defaultValue = "20") int size,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);
        
        List<Order> orders = orderService.getOrdersByStoreWithFilters(store.getId(), status, page, size);

        in.xtrs.cloudsore.dto.ApiResponse<List<Order>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(orders, "Store orders retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Update order status",
        description = "Update the status of an order in the store"
    )
    @PutMapping("/orders/{orderId}/status")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Order>> updateOrderStatus(
            @PathVariable Long orderId,
            @RequestParam OrderStatus status,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);
        
        Order order = orderService.updateOrderStatus(orderId, status, store.getId());

        in.xtrs.cloudsore.dto.ApiResponse<Order> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(order, "Order status updated successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get store analytics",
        description = "Get analytics and statistics for the store"
    )
    @GetMapping("/analytics")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<StoreAnalytics>> getStoreAnalytics(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);

        StoreAnalytics analytics = new StoreAnalytics();
        
        // Order statistics
        analytics.setTotalOrders(orderService.getOrderCountByStore(store.getId()));
        analytics.setTodayOrders(orderService.getTodayOrderCountByStore(store.getId()));
        analytics.setPendingOrders(orderService.getPendingOrderCountByStore(store.getId()));
        analytics.setCompletedOrders(orderService.getCompletedOrderCountByStore(store.getId()));

        // Product statistics
        analytics.setTotalProducts(productService.getProductCountByStore(store.getId()));
        analytics.setActiveProducts(productService.getActiveProductCountByStore(store.getId()));
        analytics.setLowStockProducts(productService.getLowStockProductCountByStore(store.getId()));

        // Category statistics
        analytics.setTotalCategories(productService.getCategoryCountByStore(store.getId()));
        analytics.setActiveCategories(productService.getActiveCategoryCountByStore(store.getId()));

        in.xtrs.cloudsore.dto.ApiResponse<StoreAnalytics> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(analytics, "Store analytics retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get low stock products",
        description = "Get products with low stock in the store"
    )
    @GetMapping("/low-stock-products")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Product>>> getLowStockProducts(
            @RequestParam(required = false, defaultValue = "10") int threshold,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);
        
        List<Product> lowStockProducts = productService.getLowStockProducts(store.getId(), threshold);

        in.xtrs.cloudsore.dto.ApiResponse<List<Product>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(lowStockProducts, "Low stock products retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get recent orders",
        description = "Get recent orders for the store"
    )
    @GetMapping("/recent-orders")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Order>>> getRecentOrders(
            @RequestParam(required = false, defaultValue = "10") int limit,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);
        
        List<Order> recentOrders = orderService.getRecentOrdersByStore(store.getId(), limit);

        in.xtrs.cloudsore.dto.ApiResponse<List<Order>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(recentOrders, "Recent orders retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get delivery managers",
        description = "Get all available delivery managers for assignment"
    )
    @GetMapping("/delivery-managers")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<User>>> getDeliveryManagers() {

        List<User> deliveryManagers = userService.getActiveDeliveryManagers();

        in.xtrs.cloudsore.dto.ApiResponse<List<User>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(deliveryManagers, "Delivery managers retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get orders ready for delivery",
        description = "Get orders that are ready to be assigned for delivery"
    )
    @GetMapping("/orders-ready-for-delivery")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Order>>> getOrdersReadyForDelivery(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);
        
        List<Order> readyOrders = orderService.getOrdersReadyForDelivery(store.getId());

        in.xtrs.cloudsore.dto.ApiResponse<List<Order>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(readyOrders, "Orders ready for delivery retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Update store information",
        description = "Update store details"
    )
    @PutMapping("/store-info")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Store>> updateStoreInfo(
            @RequestBody UpdateStoreInfoRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findByStoreManager(currentUser);
        
        Store updatedStore = storeService.updateStoreInfo(
            store.getId(),
            request.getName(),
            request.getDescription(),
            request.getAddress(),
            request.getPhoneNumber()
        );

        in.xtrs.cloudsore.dto.ApiResponse<Store> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(updatedStore, "Store information updated successfully");

        return ResponseEntity.ok(response);
    }
}
