package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
public class UserController {

    private final UserService userService;
    private final AuthService authService;

    @GetMapping("/profile")
    @PreAuthorize("hasAnyRole('SUPERADMIN', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'USER')")
    public ResponseEntity<User> getProfile(@RequestHeader("Authorization") String token) {
        try {
            User user = authService.getCurrentUser(token);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            log.error("Get profile failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<List<User>> getAllUsers() {
        try {
            List<User> users = userService.getAllUsers();
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            log.error("Get all users failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/store-managers")
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<List<User>> getStoreManagers() {
        try {
            List<User> storeManagers = userService.getUsersByRole(Role.STORE_MANAGER);
            return ResponseEntity.ok(storeManagers);
        } catch (Exception e) {
            log.error("Get store managers failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<User> getUser(@PathVariable Long id) {
        try {
            User user = userService.findById(id);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            log.error("Get user failed: {}", e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<Void> deleteUser(@PathVariable Long id) {
        try {
            userService.deleteUser(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Delete user failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}
