package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.ApiResponse;
import in.xtrs.cloudsore.dto.CreateTaxRequest;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.Tax;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.TaxType;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.StoreService;
import in.xtrs.cloudsore.service.TaxService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/stores/{storeId}/taxes")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "Tax Management", description = "APIs for managing store taxes")
@SecurityRequirement(name = "Bearer Authentication")
public class TaxController {

    private final TaxService taxService;
    private final StoreService storeService;
    private final AuthService authService;

    @Operation(summary = "Create a new tax", description = "Create a new tax for a store. Only SUPERADMIN and STORE_MANAGER can create taxes.")
    @PostMapping
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<Tax>> createTax(
            @PathVariable Long storeId,
            @Valid @RequestBody CreateTaxRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        Store store = storeService.findById(storeId);
        Tax tax = taxService.createTax(
                request.getName(),
                request.getTaxType(),
                request.getTaxRate(),
                request.getDescription(),
                store
        );

        ApiResponse<Tax> response = ApiResponse.success(tax, "Tax created successfully", 201);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(summary = "Get all taxes for a store", description = "Retrieve all active taxes for a specific store.")
    @GetMapping
    @PreAuthorize("hasRole('SUPERADMIN') or hasRole('STORE_MANAGER') or hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Tax>>> getStoreTaxes(@PathVariable Long storeId) {
        Store store = storeService.findById(storeId);
        List<Tax> taxes = taxService.findByStore(store);

        ApiResponse<List<Tax>> response = ApiResponse.success(taxes, "Taxes retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get taxes by type", description = "Retrieve taxes of a specific type for a store.")
    @GetMapping("/type/{taxType}")
    @PreAuthorize("hasRole('SUPERADMIN') or hasRole('STORE_MANAGER') or hasRole('USER')")
    public ResponseEntity<ApiResponse<List<Tax>>> getTaxesByType(
            @PathVariable Long storeId,
            @PathVariable TaxType taxType) {
        
        Store store = storeService.findById(storeId);
        List<Tax> taxes = taxService.findByStoreAndTaxType(store, taxType);

        ApiResponse<List<Tax>> response = ApiResponse.success(taxes, "Taxes retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update a tax", description = "Update an existing tax. Only SUPERADMIN and STORE_MANAGER can update taxes.")
    @PutMapping("/{taxId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<Tax>> updateTax(
            @PathVariable Long storeId,
            @PathVariable Long taxId,
            @Valid @RequestBody CreateTaxRequest request) {

        Tax tax = taxService.updateTax(
                taxId,
                request.getName(),
                request.getTaxType(),
                request.getTaxRate(),
                request.getDescription()
        );

        ApiResponse<Tax> response = ApiResponse.success(tax, "Tax updated successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Activate a tax", description = "Activate a deactivated tax.")
    @PutMapping("/{taxId}/activate")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<String>> activateTax(
            @PathVariable Long storeId,
            @PathVariable Long taxId) {

        taxService.activateTax(taxId);
        ApiResponse<String> response = ApiResponse.success(null, "Tax activated successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Deactivate a tax", description = "Deactivate an active tax.")
    @PutMapping("/{taxId}/deactivate")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<String>> deactivateTax(
            @PathVariable Long storeId,
            @PathVariable Long taxId) {

        taxService.deactivateTax(taxId);
        ApiResponse<String> response = ApiResponse.success(null, "Tax deactivated successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete a tax", description = "Delete a tax (soft delete).")
    @DeleteMapping("/{taxId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<String>> deleteTax(
            @PathVariable Long storeId,
            @PathVariable Long taxId) {

        taxService.deleteTax(taxId);
        ApiResponse<String> response = ApiResponse.success(null, "Tax deleted successfully");
        return ResponseEntity.ok(response);
    }
}
