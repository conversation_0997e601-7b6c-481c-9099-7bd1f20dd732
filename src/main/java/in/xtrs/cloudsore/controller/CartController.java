package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.*;
import in.xtrs.cloudsore.entity.*;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.CartService;
import in.xtrs.cloudsore.service.StoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/cart")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "Cart Management", description = "APIs for managing shopping cart")
@SecurityRequirement(name = "Bearer Authentication")
public class CartController {

    private final CartService cartService;
    private final StoreService storeService;
    private final AuthService authService;

    @Operation(
        summary = "Add item to cart",
        description = "Add a product to the user's shopping cart for a specific store"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Item added to cart successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request or insufficient stock"),
        @ApiResponse(responseCode = "404", description = "Product or store not found")
    })
    @PostMapping("/add")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<CartItem>> addToCart(
            @Valid @RequestBody AddToCartRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findById(request.getStoreId());

        CartItem cartItem = cartService.addToCart(
            currentUser,
            store,
            request.getProductId(),
            request.getQuantity()
        );

        in.xtrs.cloudsore.dto.ApiResponse<CartItem> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(cartItem, "Item added to cart successfully", 201);

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(
        summary = "Get cart items",
        description = "Get all items in the user's cart for a specific store"
    )
    @GetMapping("/store/{storeId}")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<CartItem>>> getCartItems(
            @PathVariable Long storeId,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        List<CartItem> cartItems = cartService.getCartItems(currentUser, storeId);

        in.xtrs.cloudsore.dto.ApiResponse<List<CartItem>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(cartItems, "Cart items retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get all user carts",
        description = "Get all active carts for the current user across all stores"
    )
    @GetMapping("/all")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Cart>>> getAllUserCarts(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        List<Cart> carts = cartService.getUserCarts(currentUser);

        in.xtrs.cloudsore.dto.ApiResponse<List<Cart>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(carts, "User carts retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Update cart item quantity",
        description = "Update the quantity of a specific item in the cart"
    )
    @PutMapping("/item/{cartItemId}")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<CartItem>> updateCartItem(
            @PathVariable Long cartItemId,
            @Valid @RequestBody UpdateCartItemRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        CartItem cartItem = cartService.updateCartItem(currentUser, cartItemId, request.getQuantity());

        in.xtrs.cloudsore.dto.ApiResponse<CartItem> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(cartItem, "Cart item updated successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Remove item from cart",
        description = "Remove a specific item from the cart"
    )
    @DeleteMapping("/item/{cartItemId}")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<String>> removeFromCart(
            @PathVariable Long cartItemId,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        cartService.removeFromCart(currentUser, cartItemId);

        in.xtrs.cloudsore.dto.ApiResponse<String> response =
            in.xtrs.cloudsore.dto.ApiResponse.success("Item removed from cart successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Clear cart",
        description = "Remove all items from the cart for a specific store"
    )
    @DeleteMapping("/store/{storeId}/clear")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<String>> clearCart(
            @PathVariable Long storeId,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        cartService.clearCart(currentUser, storeId);

        in.xtrs.cloudsore.dto.ApiResponse<String> response =
            in.xtrs.cloudsore.dto.ApiResponse.success("Cart cleared successfully");

        return ResponseEntity.ok(response);
    }
}
