package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.ApiResponse;
import in.xtrs.cloudsore.dto.CreateDeliverySlotRequest;
import in.xtrs.cloudsore.entity.DeliverySlot;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.service.DeliverySlotService;
import in.xtrs.cloudsore.service.StoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequestMapping("/api/stores/{storeId}/delivery-slots")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "Delivery Slot Management", description = "APIs for managing delivery time slots")
@SecurityRequirement(name = "Bearer Authentication")
public class DeliverySlotController {

    private final DeliverySlotService deliverySlotService;
    private final StoreService storeService;

    @Operation(summary = "Create delivery slot", description = "Create a new delivery time slot for a store. Only SUPERADMIN and STORE_MANAGER can create slots.")
    @PostMapping
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<DeliverySlot>> createDeliverySlot(
            @PathVariable Long storeId,
            @Valid @RequestBody CreateDeliverySlotRequest request) {

        Store store = storeService.findById(storeId);
        DeliverySlot slot = deliverySlotService.createDeliverySlot(
                store,
                request.getDeliveryDate(),
                request.getStartTime(),
                request.getEndTime(),
                request.getMaxOrders()
        );

        ApiResponse<DeliverySlot> response = ApiResponse.success(slot, "Delivery slot created successfully", 201);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(summary = "Get available delivery slots", description = "Get all available delivery slots for a specific date.")
    @GetMapping("/available")
    public ResponseEntity<ApiResponse<List<DeliverySlot>>> getAvailableSlots(
            @PathVariable Long storeId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date) {

        List<DeliverySlot> slots = deliverySlotService.findAvailableSlotsByStoreAndDate(storeId, date);
        ApiResponse<List<DeliverySlot>> response = ApiResponse.success(slots, "Available slots retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get bookable delivery slots", description = "Get all bookable delivery slots for a store (from today onwards).")
    @GetMapping("/bookable")
    public ResponseEntity<ApiResponse<List<DeliverySlot>>> getBookableSlots(@PathVariable Long storeId) {
        List<DeliverySlot> slots = deliverySlotService.findBookableSlotsByStore(storeId);
        ApiResponse<List<DeliverySlot>> response = ApiResponse.success(slots, "Bookable slots retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get delivery slots by date range", description = "Get delivery slots for a specific date range.")
    @GetMapping("/range")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<List<DeliverySlot>>> getSlotsByDateRange(
            @PathVariable Long storeId,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {

        List<DeliverySlot> slots = deliverySlotService.findSlotsByStoreAndDateRange(storeId, startDate, endDate);
        ApiResponse<List<DeliverySlot>> response = ApiResponse.success(slots, "Slots retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update delivery slot", description = "Update an existing delivery slot.")
    @PutMapping("/{slotId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<DeliverySlot>> updateDeliverySlot(
            @PathVariable Long storeId,
            @PathVariable Long slotId,
            @Valid @RequestBody CreateDeliverySlotRequest request) {

        DeliverySlot slot = deliverySlotService.updateSlot(
                slotId,
                request.getDeliveryDate(),
                request.getStartTime(),
                request.getEndTime(),
                request.getMaxOrders()
        );

        ApiResponse<DeliverySlot> response = ApiResponse.success(slot, "Delivery slot updated successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Activate delivery slot", description = "Activate a deactivated delivery slot.")
    @PutMapping("/{slotId}/activate")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<String>> activateSlot(
            @PathVariable Long storeId,
            @PathVariable Long slotId) {

        deliverySlotService.activateSlot(slotId);
        ApiResponse<String> response = ApiResponse.success(null, "Delivery slot activated successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Deactivate delivery slot", description = "Deactivate an active delivery slot.")
    @PutMapping("/{slotId}/deactivate")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<String>> deactivateSlot(
            @PathVariable Long storeId,
            @PathVariable Long slotId) {

        deliverySlotService.deactivateSlot(slotId);
        ApiResponse<String> response = ApiResponse.success(null, "Delivery slot deactivated successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Delete delivery slot", description = "Delete a delivery slot (only if no bookings exist).")
    @DeleteMapping("/{slotId}")
    @PreAuthorize("hasRole('SUPERADMIN') or (hasRole('STORE_MANAGER') and @storeService.isStoreManager(authentication.principal, #storeId))")
    public ResponseEntity<ApiResponse<String>> deleteSlot(
            @PathVariable Long storeId,
            @PathVariable Long slotId) {

        deliverySlotService.deleteSlot(slotId);
        ApiResponse<String> response = ApiResponse.success(null, "Delivery slot deleted successfully");
        return ResponseEntity.ok(response);
    }
}
