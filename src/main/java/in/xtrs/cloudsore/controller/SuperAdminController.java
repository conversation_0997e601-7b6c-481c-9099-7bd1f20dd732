package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.*;
import in.xtrs.cloudsore.entity.*;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/superadmin")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "Super Admin", description = "APIs for super admin management")
@SecurityRequirement(name = "Bearer Authentication")
@PreAuthorize("hasRole('SUPERADMIN')")
public class SuperAdminController {

    private final UserService userService;
    private final StoreService storeService;
    private final OrderService orderService;
    private final AuthService authService;

    @Operation(
        summary = "Get all users",
        description = "Get all users in the system with optional role filtering"
    )
    @GetMapping("/users")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<User>>> getAllUsers(
            @RequestParam(required = false) Role role,
            @RequestParam(required = false, defaultValue = "0") int page,
            @RequestParam(required = false, defaultValue = "20") int size) {

        List<User> users;
        if (role != null) {
            users = userService.getUsersByRole(role);
        } else {
            users = userService.getAllUsers();
        }

        in.xtrs.cloudsore.dto.ApiResponse<List<User>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(users, "Users retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Update user role",
        description = "Update the role of a specific user"
    )
    @PutMapping("/users/{userId}/role")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<User>> updateUserRole(
            @PathVariable Long userId,
            @Valid @RequestBody UpdateUserRoleRequest request) {

        User user = userService.updateUserRole(userId, request.getRole());

        in.xtrs.cloudsore.dto.ApiResponse<User> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(user, "User role updated successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Create store manager",
        description = "Create a new store manager and assign them to a store"
    )
    @PostMapping("/store-managers")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<User>> createStoreManager(
            @Valid @RequestBody CreateStoreManagerRequest request) {

        User storeManager = userService.createStoreManager(
            request.getName(),
            request.getEmail(),
            request.getPhoneNumber(),
            request.getStoreId()
        );

        in.xtrs.cloudsore.dto.ApiResponse<User> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(storeManager, "Store manager created successfully", 201);

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(
        summary = "Create delivery manager",
        description = "Create a new delivery manager"
    )
    @PostMapping("/delivery-managers")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<User>> createDeliveryManager(
            @Valid @RequestBody CreateDeliveryManagerRequest request) {

        User deliveryManager = userService.createDeliveryManager(
            request.getName(),
            request.getEmail(),
            request.getPhoneNumber()
        );

        in.xtrs.cloudsore.dto.ApiResponse<User> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(deliveryManager, "Delivery manager created successfully", 201);

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(
        summary = "Get all stores",
        description = "Get all stores in the system"
    )
    @GetMapping("/stores")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Store>>> getAllStores(
            @RequestParam(required = false) Boolean isActive) {

        List<Store> stores;
        if (isActive != null) {
            stores = storeService.getStoresByStatus(isActive);
        } else {
            stores = storeService.getAllStores();
        }

        in.xtrs.cloudsore.dto.ApiResponse<List<Store>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(stores, "Stores retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Activate/Deactivate store",
        description = "Activate or deactivate a store"
    )
    @PutMapping("/stores/{storeId}/status")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Store>> updateStoreStatus(
            @PathVariable Long storeId,
            @Valid @RequestBody UpdateStoreStatusRequest request) {

        Store store = storeService.updateStoreStatus(storeId, request.getIsActive());

        in.xtrs.cloudsore.dto.ApiResponse<Store> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(store, "Store status updated successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get system analytics",
        description = "Get overall system analytics and statistics"
    )
    @GetMapping("/analytics")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<SystemAnalytics>> getSystemAnalytics() {

        SystemAnalytics analytics = new SystemAnalytics();
        
        // User statistics
        analytics.setTotalUsers(userService.getTotalUserCount());
        analytics.setActiveUsers(userService.getActiveUserCount());
        analytics.setStoreManagers(userService.getUserCountByRole(Role.STORE_MANAGER));
        analytics.setDeliveryManagers(userService.getUserCountByRole(Role.DELIVERY_MANAGER));
        analytics.setCustomers(userService.getUserCountByRole(Role.USER));

        // Store statistics
        analytics.setTotalStores(storeService.getTotalStoreCount());
        analytics.setActiveStores(storeService.getActiveStoreCount());

        // Order statistics
        analytics.setTotalOrders(orderService.getTotalOrderCount());
        analytics.setTodayOrders(orderService.getTodayOrderCount());
        analytics.setPendingOrders(orderService.getPendingOrderCount());
        analytics.setCompletedOrders(orderService.getCompletedOrderCount());

        in.xtrs.cloudsore.dto.ApiResponse<SystemAnalytics> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(analytics, "System analytics retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get all orders",
        description = "Get all orders in the system with optional filtering"
    )
    @GetMapping("/orders")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Order>>> getAllOrders(
            @RequestParam(required = false) Long storeId,
            @RequestParam(required = false) String status,
            @RequestParam(required = false, defaultValue = "0") int page,
            @RequestParam(required = false, defaultValue = "20") int size) {

        List<Order> orders = orderService.getAllOrdersWithFilters(storeId, status, page, size);

        in.xtrs.cloudsore.dto.ApiResponse<List<Order>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(orders, "Orders retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Activate/Deactivate user",
        description = "Activate or deactivate a user account"
    )
    @PutMapping("/users/{userId}/status")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<User>> updateUserStatus(
            @PathVariable Long userId,
            @Valid @RequestBody UpdateUserStatusRequest request) {

        User user = userService.updateUserStatus(userId, request.getIsActive());

        in.xtrs.cloudsore.dto.ApiResponse<User> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(user, "User status updated successfully");

        return ResponseEntity.ok(response);
    }
}
