package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.ApiResponse;
import in.xtrs.cloudsore.dto.AssignDeliveryRequest;
import in.xtrs.cloudsore.dto.UpdateDeliveryStatusRequest;
import in.xtrs.cloudsore.entity.DeliveryAssignment;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.DeliveryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/api/delivery")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "Delivery Management", description = "APIs for delivery managers to handle deliveries")
@SecurityRequirement(name = "Bearer Authentication")
public class DeliveryController {

    private final DeliveryService deliveryService;
    private final AuthService authService;

    @Operation(summary = "Assign delivery to delivery manager", description = "Assign an order to a delivery manager. Only SUPERADMIN and STORE_MANAGER can assign deliveries.")
    @PostMapping("/assign")
    @PreAuthorize("hasRole('SUPERADMIN') or hasRole('STORE_MANAGER')")
    public ResponseEntity<ApiResponse<DeliveryAssignment>> assignDelivery(
            @Valid @RequestBody AssignDeliveryRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        DeliveryAssignment assignment = deliveryService.assignDelivery(
                request.getOrderId(),
                request.getDeliveryManagerId(),
                request.getNotes()
        );

        ApiResponse<DeliveryAssignment> response = ApiResponse.success(assignment, "Delivery assigned successfully", 201);
        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(summary = "Get my pending deliveries", description = "Get all pending deliveries assigned to the current delivery manager.")
    @GetMapping("/my-pending")
    @PreAuthorize("hasRole('DELIVERY_MANAGER')")
    public ResponseEntity<ApiResponse<List<DeliveryAssignment>>> getMyPendingDeliveries(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        List<DeliveryAssignment> assignments = deliveryService.findPendingByDeliveryManager(currentUser.getId());

        ApiResponse<List<DeliveryAssignment>> response = ApiResponse.success(assignments, "Pending deliveries retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get my completed deliveries", description = "Get all completed deliveries by the current delivery manager.")
    @GetMapping("/my-completed")
    @PreAuthorize("hasRole('DELIVERY_MANAGER')")
    public ResponseEntity<ApiResponse<List<DeliveryAssignment>>> getMyCompletedDeliveries(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        List<DeliveryAssignment> assignments = deliveryService.findCompletedByDeliveryManager(currentUser.getId());

        ApiResponse<List<DeliveryAssignment>> response = ApiResponse.success(assignments, "Completed deliveries retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Mark order as picked up", description = "Mark an assigned order as picked up.")
    @PutMapping("/{assignmentId}/pickup")
    @PreAuthorize("hasRole('DELIVERY_MANAGER')")
    public ResponseEntity<ApiResponse<DeliveryAssignment>> markAsPickedUp(
            @PathVariable Long assignmentId,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        DeliveryAssignment assignment = deliveryService.findById(assignmentId);
        
        // Ensure the delivery manager can only update their own assignments
        if (!assignment.getDeliveryManager().getId().equals(currentUser.getId())) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }

        DeliveryAssignment updatedAssignment = deliveryService.markAsPickedUp(assignmentId);
        ApiResponse<DeliveryAssignment> response = ApiResponse.success(updatedAssignment, "Order marked as picked up successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Mark order as delivered", description = "Mark an assigned order as delivered with feedback.")
    @PutMapping("/{assignmentId}/deliver")
    @PreAuthorize("hasRole('DELIVERY_MANAGER')")
    public ResponseEntity<ApiResponse<DeliveryAssignment>> markAsDelivered(
            @PathVariable Long assignmentId,
            @Valid @RequestBody UpdateDeliveryStatusRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        DeliveryAssignment assignment = deliveryService.findById(assignmentId);
        
        // Ensure the delivery manager can only update their own assignments
        if (!assignment.getDeliveryManager().getId().equals(currentUser.getId())) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }

        DeliveryAssignment updatedAssignment = deliveryService.markAsDelivered(
                assignmentId,
                request.getDeliveryNotes(),
                request.getCustomerFeedback(),
                request.getDeliveryRating()
        );

        ApiResponse<DeliveryAssignment> response = ApiResponse.success(updatedAssignment, "Order marked as delivered successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Update delivery notes", description = "Update delivery notes for an assignment.")
    @PutMapping("/{assignmentId}/notes")
    @PreAuthorize("hasRole('DELIVERY_MANAGER')")
    public ResponseEntity<ApiResponse<DeliveryAssignment>> updateDeliveryNotes(
            @PathVariable Long assignmentId,
            @RequestParam String notes,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        DeliveryAssignment assignment = deliveryService.findById(assignmentId);
        
        // Ensure the delivery manager can only update their own assignments
        if (!assignment.getDeliveryManager().getId().equals(currentUser.getId())) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
        }

        DeliveryAssignment updatedAssignment = deliveryService.updateDeliveryNotes(assignmentId, notes);
        ApiResponse<DeliveryAssignment> response = ApiResponse.success(updatedAssignment, "Delivery notes updated successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get delivery history", description = "Get delivery history for a date range.")
    @GetMapping("/history")
    @PreAuthorize("hasRole('DELIVERY_MANAGER')")
    public ResponseEntity<ApiResponse<List<DeliveryAssignment>>> getDeliveryHistory(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        List<DeliveryAssignment> assignments = deliveryService.getDeliveryHistory(currentUser.getId(), startDate, endDate);

        ApiResponse<List<DeliveryAssignment>> response = ApiResponse.success(assignments, "Delivery history retrieved successfully");
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "Get pending delivery count", description = "Get count of pending deliveries for the current delivery manager.")
    @GetMapping("/pending-count")
    @PreAuthorize("hasRole('DELIVERY_MANAGER')")
    public ResponseEntity<ApiResponse<Long>> getPendingDeliveryCount(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Long count = deliveryService.getPendingDeliveryCount(currentUser.getId());

        ApiResponse<Long> response = ApiResponse.success(count, "Pending delivery count retrieved successfully");
        return ResponseEntity.ok(response);
    }
}
