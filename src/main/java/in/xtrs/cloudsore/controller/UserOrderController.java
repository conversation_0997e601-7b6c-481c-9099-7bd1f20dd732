package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.*;
import in.xtrs.cloudsore.entity.*;
import in.xtrs.cloudsore.enums.OrderStatus;
import in.xtrs.cloudsore.service.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/user/orders")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "User Orders", description = "APIs for user order management and history")
@SecurityRequirement(name = "Bearer Authentication")
public class UserOrderController {

    private final OrderService orderService;
    private final CartService cartService;
    private final StoreService storeService;
    private final AuthService authService;
    private final DeliverySlotService deliverySlotService;

    @Operation(
        summary = "Create order from cart",
        description = "Create a new order from items in the user's cart"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Order created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request or empty cart"),
        @ApiResponse(responseCode = "404", description = "Cart or store not found")
    })
    @PostMapping("/create-from-cart")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Order>> createOrderFromCart(
            @Valid @RequestBody CreateOrderFromCartRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findById(request.getStoreId());

        // Get cart items
        List<CartItem> cartItems = cartService.getCartItems(currentUser, request.getStoreId());
        if (cartItems.isEmpty()) {
            throw new RuntimeException("Cart is empty");
        }

        // Create order
        Order order = orderService.createOrder(
            currentUser,
            store,
            request.getDeliveryAddress(),
            request.getDeliveryPhone(),
            request.getNotes()
        );

        // Set delivery slot if provided
        if (request.getDeliverySlotId() != null) {
            DeliverySlot deliverySlot = deliverySlotService.findById(request.getDeliverySlotId());
            order.setDeliverySlot(deliverySlot);
        }

        // Add cart items to order
        for (CartItem cartItem : cartItems) {
            orderService.addOrderItem(
                order.getId(),
                cartItem.getProduct().getId(),
                cartItem.getQuantity()
            );
        }

        // Clear cart after successful order creation
        cartService.clearCart(currentUser, request.getStoreId());

        in.xtrs.cloudsore.dto.ApiResponse<Order> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(order, "Order created successfully", 201);

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(
        summary = "Get user order history",
        description = "Get all orders placed by the current user"
    )
    @GetMapping("/history")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Order>>> getOrderHistory(
            @RequestParam(required = false) Long storeId,
            @RequestParam(required = false) OrderStatus status,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        List<Order> orders = orderService.getOrdersByUser(currentUser, storeId, status);

        in.xtrs.cloudsore.dto.ApiResponse<List<Order>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(orders, "Order history retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get order details",
        description = "Get detailed information about a specific order"
    )
    @GetMapping("/{orderId}")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Order>> getOrderDetails(
            @PathVariable Long orderId,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Order order = orderService.findById(orderId);

        // Verify user owns this order
        if (!order.getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You can only view your own orders");
        }

        in.xtrs.cloudsore.dto.ApiResponse<Order> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(order, "Order details retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Track order",
        description = "Get tracking information for a specific order"
    )
    @GetMapping("/{orderId}/track")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<OrderTrackingInfo>> trackOrder(
            @PathVariable Long orderId,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Order order = orderService.findById(orderId);

        // Verify user owns this order
        if (!order.getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You can only track your own orders");
        }

        OrderTrackingInfo trackingInfo = new OrderTrackingInfo();
        trackingInfo.setOrderId(order.getId());
        trackingInfo.setOrderNumber(order.getOrderNumber());
        trackingInfo.setStatus(order.getStatus());
        trackingInfo.setTrackingNumber(order.getTrackingNumber());
        trackingInfo.setEstimatedDeliveryTime(order.getEstimatedDeliveryTime());
        trackingInfo.setActualDeliveryTime(order.getActualDeliveryTime());
        trackingInfo.setCreatedAt(order.getCreatedAt());
        trackingInfo.setUpdatedAt(order.getUpdatedAt());

        if (order.getDeliveryAssignment() != null) {
            trackingInfo.setDeliveryManagerName(order.getDeliveryAssignment().getDeliveryManager().getName());
            trackingInfo.setDeliveryManagerPhone(order.getDeliveryAssignment().getDeliveryManager().getPhoneNumber());
            trackingInfo.setPickedUpAt(order.getDeliveryAssignment().getPickedUpAt());
            trackingInfo.setDeliveredAt(order.getDeliveryAssignment().getDeliveredAt());
            trackingInfo.setDeliveryNotes(order.getDeliveryAssignment().getDeliveryNotes());
        }

        in.xtrs.cloudsore.dto.ApiResponse<OrderTrackingInfo> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(trackingInfo, "Order tracking information retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Cancel order",
        description = "Cancel an order if it's still cancellable"
    )
    @PutMapping("/{orderId}/cancel")
    @PreAuthorize("hasAnyRole('USER', 'STORE_MANAGER', 'DELIVERY_MANAGER', 'SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<String>> cancelOrder(
            @PathVariable Long orderId,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Order order = orderService.findById(orderId);

        // Verify user owns this order
        if (!order.getUser().getId().equals(currentUser.getId())) {
            throw new RuntimeException("You can only cancel your own orders");
        }

        // Check if order can be cancelled
        if (order.getStatus() == OrderStatus.DELIVERED || order.getStatus() == OrderStatus.CANCELLED) {
            throw new RuntimeException("Order cannot be cancelled in current status: " + order.getStatus());
        }

        orderService.cancelOrder(orderId);

        in.xtrs.cloudsore.dto.ApiResponse<String> response =
            in.xtrs.cloudsore.dto.ApiResponse.success("Order cancelled successfully");

        return ResponseEntity.ok(response);
    }
}
