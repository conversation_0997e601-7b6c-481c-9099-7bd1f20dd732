package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.*;
import in.xtrs.cloudsore.entity.Order;
import in.xtrs.cloudsore.entity.OrderItem;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.OrderStatus;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.exception.ForbiddenException;
import in.xtrs.cloudsore.service.AuthService;
import in.xtrs.cloudsore.service.OrderService;
import in.xtrs.cloudsore.service.StoreService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/orders")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@PreAuthorize("hasAnyRole('SUPERADMIN', 'ADMIN')") // USER role cannot access orders as per requirement
@Tag(name = "Order Management", description = "APIs for managing orders and order items")
@SecurityRequirement(name = "Bearer Authentication")
public class OrderController {

    private final OrderService orderService;
    private final StoreService storeService;
    private final AuthService authService;

    @Operation(
        summary = "Create a new order",
        description = "Create a new order for a specific store. Only SUPERADMIN can create orders."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Order created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Store not found")
    })
    @PostMapping
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Order>> createOrder(
            @Valid @RequestBody CreateOrderRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Store store = storeService.findById(request.getStoreId());

        Order order = orderService.createOrder(
            currentUser,
            store,
            request.getDeliveryAddress(),
            request.getDeliveryPhone(),
            request.getNotes()
        );

        in.xtrs.cloudsore.dto.ApiResponse<Order> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(order, "Order created successfully", 201);

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(
        summary = "Get orders",
        description = "Get orders based on user role. ADMIN can only see orders from their store, SUPERADMIN can filter by store or status."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Orders retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Store not found")
    })
    @GetMapping
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<Order>>> getOrders(
            @Parameter(hidden = true) @RequestHeader("Authorization") String token,
            @Parameter(description = "Filter by store ID (SUPERADMIN only)") @RequestParam(required = false) Long storeId,
            @Parameter(description = "Filter by order status (SUPERADMIN only)") @RequestParam(required = false) OrderStatus status) {

        User currentUser = authService.getCurrentUser(token);
        List<Order> orders;

        if (currentUser.getRole() == Role.ADMIN) {
            // Admin can only see orders from their store
            Store store = storeService.findByAdmin(currentUser);
            orders = orderService.getOrdersByStore(store);
        } else {
            // SUPERADMIN can see all orders with optional filters
            if (storeId != null) {
                Store store = storeService.findById(storeId);
                orders = orderService.getOrdersByStore(store);
            } else if (status != null) {
                orders = orderService.getOrdersByStatus(status);
            } else {
                orders = orderService.getAllOrders();
            }
        }

        in.xtrs.cloudsore.dto.ApiResponse<List<Order>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(orders, "Orders retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get order by ID",
        description = "Get a specific order by ID. ADMIN can only access orders from their store."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Order not found")
    })
    @GetMapping("/{id}")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Order>> getOrder(
            @Parameter(description = "Order ID") @PathVariable Long id,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        Order order = orderService.findById(id);
        User currentUser = authService.getCurrentUser(token);

        // Check if store manager can access this order (must be from their store)
        if (currentUser.getRole() == Role.STORE_MANAGER) {
            Store adminStore = storeService.findByAdmin(currentUser);
            if (!order.getStore().getId().equals(adminStore.getId())) {
                throw new ForbiddenException("You can only access orders from your store");
            }
        }

        in.xtrs.cloudsore.dto.ApiResponse<Order> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(order, "Order retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Get order items",
        description = "Get all items for a specific order. ADMIN can only access orders from their store."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order items retrieved successfully"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Order not found")
    })
    @GetMapping("/{id}/items")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<List<OrderItem>>> getOrderItems(
            @Parameter(description = "Order ID") @PathVariable Long id,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        Order order = orderService.findById(id);
        User currentUser = authService.getCurrentUser(token);

        // Check if admin can access this order
        if (currentUser.getRole() == Role.ADMIN) {
            Store adminStore = storeService.findByAdmin(currentUser);
            if (!order.getStore().getId().equals(adminStore.getId())) {
                throw new ForbiddenException("You can only access orders from your store");
            }
        }

        List<OrderItem> orderItems = orderService.getOrderItems(id);

        in.xtrs.cloudsore.dto.ApiResponse<List<OrderItem>> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(orderItems, "Order items retrieved successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Add item to order",
        description = "Add a product item to an existing order. ADMIN can only modify orders from their store."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Order item added successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid request data or insufficient stock"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Order or product not found")
    })
    @PostMapping("/{id}/items")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<OrderItem>> addOrderItem(
            @Parameter(description = "Order ID") @PathVariable Long id,
            @Valid @RequestBody AddOrderItemRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        Order order = orderService.findById(id);
        User currentUser = authService.getCurrentUser(token);

        // Check if admin can modify this order
        if (currentUser.getRole() == Role.ADMIN) {
            Store adminStore = storeService.findByAdmin(currentUser);
            if (!order.getStore().getId().equals(adminStore.getId())) {
                throw new ForbiddenException("You can only modify orders from your store");
            }
        }

        OrderItem orderItem = orderService.addOrderItem(id, request.getProductId(), request.getQuantity());

        in.xtrs.cloudsore.dto.ApiResponse<OrderItem> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(orderItem, "Order item added successfully", 201);

        return new ResponseEntity<>(response, HttpStatus.CREATED);
    }

    @Operation(
        summary = "Update order status",
        description = "Update the status of an order. ADMIN can only update orders from their store."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order status updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid status or request data"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Order not found")
    })
    @PutMapping("/{id}/status")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<Order>> updateOrderStatus(
            @Parameter(description = "Order ID") @PathVariable Long id,
            @Valid @RequestBody UpdateOrderStatusRequest request,
            @Parameter(hidden = true) @RequestHeader("Authorization") String token) {

        User currentUser = authService.getCurrentUser(token);
        Order order = orderService.findById(id);

        // Check if admin can update this order
        if (currentUser.getRole() == Role.ADMIN) {
            Store adminStore = storeService.findByAdmin(currentUser);
            if (!order.getStore().getId().equals(adminStore.getId())) {
                throw new ForbiddenException("You can only update orders from your store");
            }
        }

        Order updatedOrder = orderService.updateOrderStatus(id, request.getStatus());

        in.xtrs.cloudsore.dto.ApiResponse<Order> response =
            in.xtrs.cloudsore.dto.ApiResponse.success(updatedOrder, "Order status updated successfully");

        return ResponseEntity.ok(response);
    }

    @Operation(
        summary = "Cancel order",
        description = "Cancel an order and restore product stock. Only SUPERADMIN can cancel orders."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Order cancelled successfully"),
        @ApiResponse(responseCode = "400", description = "Cannot cancel order (e.g., already delivered)"),
        @ApiResponse(responseCode = "403", description = "Access denied"),
        @ApiResponse(responseCode = "404", description = "Order not found")
    })
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('SUPERADMIN')")
    public ResponseEntity<in.xtrs.cloudsore.dto.ApiResponse<String>> cancelOrder(
            @Parameter(description = "Order ID") @PathVariable Long id) {

        orderService.cancelOrder(id);

        in.xtrs.cloudsore.dto.ApiResponse<String> response =
            in.xtrs.cloudsore.dto.ApiResponse.success("Order cancelled successfully", "Order cancelled successfully");

        return ResponseEntity.ok(response);
    }
}
