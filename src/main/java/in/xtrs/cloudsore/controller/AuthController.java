package in.xtrs.cloudsore.controller;

import in.xtrs.cloudsore.dto.*;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Slf4j
@CrossOrigin(origins = "*")
@Tag(name = "Authentication", description = "OTP-based authentication with JWT access and refresh tokens")
public class AuthController {

    private final AuthService authService;

    @Operation(
        summary = "Register with phone number",
        description = "Register a new user with phone number and send OTP for verification"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OTP sent successfully",
                content = @Content(schema = @Schema(implementation = AuthResponse.class))),
        @ApiResponse(responseCode = "400", description = "Registration failed",
                content = @Content(schema = @Schema(implementation = AuthResponse.class)))
    })
    @PostMapping("/register/phone")
    public ResponseEntity<AuthResponse> registerWithPhone(@Valid @RequestBody AuthRequest request) {
        try {
            String message = authService.registerWithPhone(request.getName(), request.getPhoneNumber());
            return ResponseEntity.ok(new AuthResponse(null, message, true));
        } catch (Exception e) {
            log.error("Phone registration failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, e.getMessage(), false));
        }
    }

    @Operation(
        summary = "Login with phone number",
        description = "Send OTP to existing user's phone number for authentication"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OTP sent successfully"),
        @ApiResponse(responseCode = "400", description = "User not found or OTP sending failed")
    })
    @PostMapping("/login/phone")
    public ResponseEntity<AuthResponse> loginWithPhone(@Valid @RequestBody PhoneLoginRequest request) {
        try {
            String message = authService.loginWithPhone(request.getPhoneNumber());
            return ResponseEntity.ok(new AuthResponse(null, message, true));
        } catch (Exception e) {
            log.error("Phone login failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, e.getMessage(), false));
        }
    }

    @Operation(
        summary = "Verify OTP",
        description = "Verify the OTP sent to user's phone number and return JWT access and refresh tokens"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "OTP verified successfully, tokens returned"),
        @ApiResponse(responseCode = "400", description = "Invalid OTP or verification failed")
    })
    @PostMapping("/verify/otp")
    public ResponseEntity<TokenResponse> verifyOtp(@Valid @RequestBody OtpRequest request) {
        try {
            TokenResponse tokenResponse = authService.authenticateWithPhone(request.getPhoneNumber(), request.getOtp());
            return ResponseEntity.ok(tokenResponse);
        } catch (Exception e) {
            log.error("OTP verification failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new TokenResponse(null, null, null, e.getMessage(), false));
        }
    }

    @Operation(
        summary = "Get current user",
        description = "Get current authenticated user information",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "User information retrieved successfully"),
        @ApiResponse(responseCode = "401", description = "Unauthorized - Invalid token"),
        @ApiResponse(responseCode = "400", description = "Bad request")
    })
    @GetMapping("/me")
    public ResponseEntity<User> getCurrentUser(
        @Parameter(description = "JWT Bearer token", required = true)
        @RequestHeader("Authorization") String token) {
        try {
            User user = authService.getCurrentUser(token);
            return ResponseEntity.ok(user);
        } catch (Exception e) {
            log.error("Get current user failed: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    @Operation(
        summary = "Validate JWT token",
        description = "Validate if the provided JWT token is valid and not expired",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Token is valid"),
        @ApiResponse(responseCode = "400", description = "Token is invalid or expired")
    })
    @PostMapping("/validate")
    public ResponseEntity<AuthResponse> validateToken(
        @Parameter(description = "JWT Bearer token", required = true)
        @RequestHeader("Authorization") String token) {
        try {
            boolean isValid = authService.isTokenValid(token);
            if (isValid) {
                return ResponseEntity.ok(new AuthResponse(null, "Token is valid", true));
            } else {
                return ResponseEntity.badRequest()
                        .body(new AuthResponse(null, "Token is invalid", false));
            }
        } catch (Exception e) {
            log.error("Token validation failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, "Token validation failed", false));
        }
    }

    @Operation(
        summary = "Refresh access token",
        description = "Use refresh token to get a new access token"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Access token refreshed successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid or expired refresh token")
    })
    @PostMapping("/refresh")
    public ResponseEntity<TokenResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
        try {
            TokenResponse tokenResponse = authService.refreshAccessToken(request.getRefreshToken());
            return ResponseEntity.ok(tokenResponse);
        } catch (Exception e) {
            log.error("Token refresh failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new TokenResponse(null, null, null, e.getMessage(), false));
        }
    }

    @Operation(
        summary = "Logout",
        description = "Logout user by revoking the refresh token"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Logged out successfully"),
        @ApiResponse(responseCode = "400", description = "Logout failed")
    })
    @PostMapping("/logout")
    public ResponseEntity<AuthResponse> logout(@Valid @RequestBody RefreshTokenRequest request) {
        try {
            authService.logout(request.getRefreshToken());
            return ResponseEntity.ok(new AuthResponse(null, "Logged out successfully", true));
        } catch (Exception e) {
            log.error("Logout failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, e.getMessage(), false));
        }
    }

    @Operation(
        summary = "Logout from all devices",
        description = "Logout user from all devices by revoking all refresh tokens",
        security = @SecurityRequirement(name = "Bearer Authentication")
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Logged out from all devices successfully"),
        @ApiResponse(responseCode = "400", description = "Logout failed")
    })
    @PostMapping("/logout/all")
    public ResponseEntity<AuthResponse> logoutAll(
        @Parameter(description = "JWT Bearer token", required = true)
        @RequestHeader("Authorization") String token) {
        try {
            User user = authService.getCurrentUser(token);
            authService.logoutAllDevices(user);
            return ResponseEntity.ok(new AuthResponse(null, "Logged out from all devices successfully", true));
        } catch (Exception e) {
            log.error("Logout all failed: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(new AuthResponse(null, e.getMessage(), false));
        }
    }
}
