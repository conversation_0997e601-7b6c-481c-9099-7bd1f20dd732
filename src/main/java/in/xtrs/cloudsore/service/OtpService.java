package in.xtrs.cloudsore.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class OtpService {

    @Value("${fast2sms.api.key}")
    private String fast2smsApiKey;

    @Value("${fast2sms.sender.id:FSTSMS}")
    private String senderId;

    @Value("${fast2sms.base.url:https://www.fast2sms.com/dev/bulkV2}")
    private String fast2smsBaseUrl;

    private final SecureRandom random = new SecureRandom();
    private final RestTemplate restTemplate = new RestTemplate();



    public String generateOtp() {
        int otp = 100000 + random.nextInt(900000); // 6-digit OTP
        return String.valueOf(otp);
    }

    public LocalDateTime getOtpExpiry() {
        return LocalDateTime.now().plusMinutes(5); // OTP expires in 5 minutes
    }

    public boolean sendOtp(String phoneNumber, String otp) {
        try {
            // For development/testing, log the OTP instead of sending SMS
            if (fast2smsApiKey == null || fast2smsApiKey.equals("your-fast2sms-api-key")) {
                log.info("OTP for {}: {}", phoneNumber, otp);
                return true;
            }

            // Clean phone number (remove +91 prefix for Fast2SMS)
            String cleanedPhoneNumber = phoneNumber.replaceAll("[^\\d]", "");
            if (cleanedPhoneNumber.startsWith("91")) {
                cleanedPhoneNumber = cleanedPhoneNumber.substring(2);
            }

            String messageBody = "Your CloudStore verification code is: " + otp + ". This code will expire in 5 minutes.";

            // Prepare Fast2SMS API request
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authorization", fast2smsApiKey);

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("route", "otp");
            requestBody.put("variables_values", otp);
            requestBody.put("flash", 0);
            requestBody.put("numbers", cleanedPhoneNumber);

            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);

            // Send OTP via Fast2SMS
            ResponseEntity<String> response = restTemplate.postForEntity(fast2smsBaseUrl, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("OTP sent successfully to {} via Fast2SMS", phoneNumber);
                return true;
            } else {
                log.error("Failed to send OTP to {}. Response: {}", phoneNumber, response.getBody());
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to send OTP to {}: {}", phoneNumber, e.getMessage());
            return false;
        }
    }

    public boolean isOtpValid(String providedOtp, String storedOtp, LocalDateTime otpExpiry) {
        if (storedOtp == null || otpExpiry == null) {
            return false;
        }
        
        if (LocalDateTime.now().isAfter(otpExpiry)) {
            return false;
        }
        
        return storedOtp.equals(providedOtp);
    }

    public String formatPhoneNumber(String phoneNumber) {
        // Remove all non-digit characters
        String cleaned = phoneNumber.replaceAll("[^\\d]", "");

        // Add country code if not present (assuming India +91)
        if (!cleaned.startsWith("91") && cleaned.length() == 10) {
            cleaned = "91" + cleaned;
        }

        // Add + prefix for international format
        if (!cleaned.startsWith("+")) {
            cleaned = "+" + cleaned;
        }

        return cleaned;
    }

    /**
     * Send OTP using Fast2SMS template-based approach
     * This method uses Fast2SMS OTP template for better delivery rates
     */
    public boolean sendOtpWithTemplate(String phoneNumber, String otp) {
        try {
            // For development/testing, log the OTP instead of sending SMS
            if (fast2smsApiKey == null || fast2smsApiKey.equals("your-fast2sms-api-key")) {
                log.info("OTP for {}: {}", phoneNumber, otp);
                return true;
            }

            // Clean phone number (remove +91 prefix for Fast2SMS)
            String cleanedPhoneNumber = phoneNumber.replaceAll("[^\\d]", "");
            if (cleanedPhoneNumber.startsWith("91")) {
                cleanedPhoneNumber = cleanedPhoneNumber.substring(2);
            }

            // Prepare Fast2SMS API request with template
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.set("authorization", fast2smsApiKey);

            // Using template-based OTP (you need to create a template in Fast2SMS dashboard)
            String requestBody = String.format(
                "route=otp&variables_values=%s&flash=0&numbers=%s",
                otp, cleanedPhoneNumber
            );

            HttpEntity<String> entity = new HttpEntity<>(requestBody, headers);

            // Send OTP via Fast2SMS
            ResponseEntity<String> response = restTemplate.postForEntity(fast2smsBaseUrl, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                log.info("OTP sent successfully to {} via Fast2SMS template", phoneNumber);
                log.debug("Fast2SMS Response: {}", response.getBody());
                return true;
            } else {
                log.error("Failed to send OTP to {}. Response: {}", phoneNumber, response.getBody());
                return false;
            }
        } catch (Exception e) {
            log.error("Failed to send OTP to {}: {}", phoneNumber, e.getMessage());
            return false;
        }
    }
}
