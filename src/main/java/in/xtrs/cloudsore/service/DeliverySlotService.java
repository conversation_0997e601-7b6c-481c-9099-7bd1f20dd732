package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.DeliverySlot;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.exception.BusinessLogicException;
import in.xtrs.cloudsore.exception.ResourceNotFoundException;
import in.xtrs.cloudsore.repository.DeliverySlotRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DeliverySlotService {

    private final DeliverySlotRepository deliverySlotRepository;

    public DeliverySlot createDeliverySlot(Store store, LocalDate deliveryDate, LocalTime startTime, LocalTime endTime, Integer maxOrders) {
        if (deliveryDate.isBefore(LocalDate.now())) {
            throw new BusinessLogicException("Cannot create delivery slot for past dates");
        }

        if (startTime.isAfter(endTime)) {
            throw new BusinessLogicException("Start time cannot be after end time");
        }

        DeliverySlot deliverySlot = new DeliverySlot(store, deliveryDate, startTime, endTime, maxOrders);
        return deliverySlotRepository.save(deliverySlot);
    }

    public DeliverySlot findById(Long id) {
        return deliverySlotRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Delivery slot not found with id: " + id));
    }

    public List<DeliverySlot> findAvailableSlotsByStoreAndDate(Long storeId, LocalDate date) {
        return deliverySlotRepository.findAvailableByStoreAndDate(storeId, date);
    }

    public List<DeliverySlot> findBookableSlotsByStore(Long storeId) {
        return deliverySlotRepository.findBookableSlotsByStoreFromDate(storeId, LocalDate.now());
    }

    public List<DeliverySlot> findSlotsByStoreAndDateRange(Long storeId, LocalDate startDate, LocalDate endDate) {
        return deliverySlotRepository.findByStoreAndDateRange(storeId, startDate, endDate);
    }

    public DeliverySlot bookSlot(Long slotId) {
        DeliverySlot slot = findById(slotId);
        
        if (!slot.isBookable()) {
            throw new BusinessLogicException("Delivery slot is not available for booking");
        }

        slot.setCurrentOrders(slot.getCurrentOrders() + 1);
        return deliverySlotRepository.save(slot);
    }

    public DeliverySlot releaseSlot(Long slotId) {
        DeliverySlot slot = findById(slotId);
        
        if (slot.getCurrentOrders() > 0) {
            slot.setCurrentOrders(slot.getCurrentOrders() - 1);
        }
        
        return deliverySlotRepository.save(slot);
    }

    public DeliverySlot updateSlot(Long id, LocalDate deliveryDate, LocalTime startTime, LocalTime endTime, Integer maxOrders) {
        DeliverySlot slot = findById(id);
        
        if (deliveryDate.isBefore(LocalDate.now())) {
            throw new BusinessLogicException("Cannot update delivery slot to past dates");
        }

        if (startTime.isAfter(endTime)) {
            throw new BusinessLogicException("Start time cannot be after end time");
        }

        if (maxOrders < slot.getCurrentOrders()) {
            throw new BusinessLogicException("Cannot reduce max orders below current bookings");
        }

        slot.setDeliveryDate(deliveryDate);
        slot.setStartTime(startTime);
        slot.setEndTime(endTime);
        slot.setMaxOrders(maxOrders);
        
        return deliverySlotRepository.save(slot);
    }

    public void activateSlot(Long id) {
        DeliverySlot slot = findById(id);
        slot.setIsActive(true);
        slot.setIsAvailable(true);
        deliverySlotRepository.save(slot);
    }

    public void deactivateSlot(Long id) {
        DeliverySlot slot = findById(id);
        slot.setIsActive(false);
        slot.setIsAvailable(false);
        deliverySlotRepository.save(slot);
    }

    public void deleteSlot(Long id) {
        DeliverySlot slot = findById(id);
        if (slot.getCurrentOrders() > 0) {
            throw new BusinessLogicException("Cannot delete delivery slot with existing bookings");
        }
        deliverySlotRepository.delete(slot);
    }
}
