package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.Tax;
import in.xtrs.cloudsore.enums.TaxType;
import in.xtrs.cloudsore.exception.BusinessLogicException;
import in.xtrs.cloudsore.exception.ResourceNotFoundException;
import in.xtrs.cloudsore.repository.TaxRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class TaxService {

    private final TaxRepository taxRepository;

    public Tax createTax(String name, TaxType taxType, BigDecimal taxRate, String description, Store store) {
        if (taxRepository.existsByNameAndStore(name, store)) {
            throw new BusinessLogicException("Tax with name '" + name + "' already exists for this store");
        }

        Tax tax = new Tax(name, taxType, taxRate, description, store);
        return taxRepository.save(tax);
    }

    public Tax findById(Long id) {
        return taxRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Tax not found with id: " + id));
    }

    public List<Tax> findByStore(Store store) {
        return taxRepository.findByStoreAndIsActive(store, true);
    }

    public List<Tax> findByStoreAndTaxType(Store store, TaxType taxType) {
        return taxRepository.findActiveByStoreIdAndTaxType(store.getId(), taxType);
    }

    public Tax updateTax(Long id, String name, TaxType taxType, BigDecimal taxRate, String description) {
        Tax tax = findById(id);
        
        // Check if name is being changed and if it conflicts
        if (!tax.getName().equals(name) && taxRepository.existsByNameAndStore(name, tax.getStore())) {
            throw new BusinessLogicException("Tax with name '" + name + "' already exists for this store");
        }

        tax.setName(name);
        tax.setTaxType(taxType);
        tax.setTaxRate(taxRate);
        tax.setDescription(description);
        
        return taxRepository.save(tax);
    }

    public void activateTax(Long id) {
        Tax tax = findById(id);
        tax.setIsActive(true);
        taxRepository.save(tax);
    }

    public void deactivateTax(Long id) {
        Tax tax = findById(id);
        tax.setIsActive(false);
        taxRepository.save(tax);
    }

    public void deleteTax(Long id) {
        Tax tax = findById(id);
        tax.setIsActive(false);
        taxRepository.save(tax);
    }

    public List<Tax> getAllActiveTaxes() {
        return taxRepository.findAll().stream()
                .filter(Tax::getIsActive)
                .toList();
    }
}
