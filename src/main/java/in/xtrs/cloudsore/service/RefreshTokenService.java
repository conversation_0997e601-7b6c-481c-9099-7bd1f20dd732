package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.RefreshToken;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.exception.BadRequestException;
import in.xtrs.cloudsore.exception.ResourceNotFoundException;
import in.xtrs.cloudsore.exception.UnauthorizedException;
import in.xtrs.cloudsore.repository.RefreshTokenRepository;
import in.xtrs.cloudsore.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class RefreshTokenService {

    private final RefreshTokenRepository refreshTokenRepository;
    private final JwtUtil jwtUtil;

    @Value("${app.refresh-token.max-per-user:5}")
    private int maxTokensPerUser;

    public RefreshToken createRefreshToken(User user) {
        // Clean up expired tokens first
        cleanupExpiredTokens();
        
        // Check if user has too many active tokens
        long activeTokenCount = refreshTokenRepository.countValidTokensByUser(user, LocalDateTime.now());
        if (activeTokenCount >= maxTokensPerUser) {
            // Revoke oldest tokens to make room
            revokeOldestTokensForUser(user, (int) (activeTokenCount - maxTokensPerUser + 1));
        }

        // Generate new refresh token
        String tokenValue = jwtUtil.generateRefreshToken(
            user.getPhoneNumber() != null ? user.getPhoneNumber() : user.getEmail(),
            user.getId()
        );

        LocalDateTime expiresAt = LocalDateTime.now()
            .plusSeconds(jwtUtil.getRefreshTokenExpiration() / 1000);

        RefreshToken refreshToken = new RefreshToken(tokenValue, user, expiresAt);
        return refreshTokenRepository.save(refreshToken);
    }

    public RefreshToken findByToken(String token) {
        return refreshTokenRepository.findByToken(token)
                .orElseThrow(() -> new ResourceNotFoundException("Refresh token not found"));
    }

    public RefreshToken verifyExpiration(RefreshToken token) {
        if (token.isExpired() || token.getIsRevoked()) {
            refreshTokenRepository.delete(token);
            throw new UnauthorizedException("Refresh token is expired or revoked. Please login again.");
        }
        return token;
    }

    public void revokeToken(String token) {
        RefreshToken refreshToken = findByToken(token);
        refreshToken.setIsRevoked(true);
        refreshTokenRepository.save(refreshToken);
    }

    public void revokeAllUserTokens(User user) {
        refreshTokenRepository.revokeAllUserTokens(user);
    }

    private void revokeOldestTokensForUser(User user, int count) {
        var tokens = refreshTokenRepository.findValidTokensByUser(user, LocalDateTime.now());
        tokens.stream()
                .sorted((t1, t2) -> t1.getCreatedAt().compareTo(t2.getCreatedAt()))
                .limit(count)
                .forEach(token -> {
                    token.setIsRevoked(true);
                    refreshTokenRepository.save(token);
                });
    }

    @Scheduled(fixedRate = 3600000) // Run every hour
    public void cleanupExpiredTokens() {
        try {
            refreshTokenRepository.deleteExpiredAndRevokedTokens(LocalDateTime.now());
            log.debug("Cleaned up expired and revoked refresh tokens");
        } catch (Exception e) {
            log.error("Error cleaning up expired tokens: {}", e.getMessage());
        }
    }

    public boolean isValidRefreshToken(String token) {
        try {
            if (!jwtUtil.isRefreshToken(token)) {
                return false;
            }
            
            RefreshToken refreshToken = findByToken(token);
            verifyExpiration(refreshToken);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public User getUserFromRefreshToken(String token) {
        if (!jwtUtil.isRefreshToken(token)) {
            throw new BadRequestException("Invalid token type. Expected refresh token.");
        }
        
        RefreshToken refreshToken = findByToken(token);
        verifyExpiration(refreshToken);
        return refreshToken.getUser();
    }
}
