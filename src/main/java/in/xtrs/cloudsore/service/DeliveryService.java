package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.DeliveryAssignment;
import in.xtrs.cloudsore.entity.Order;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.OrderStatus;
import in.xtrs.cloudsore.enums.Role;
import in.xtrs.cloudsore.exception.BusinessLogicException;
import in.xtrs.cloudsore.exception.ResourceNotFoundException;
import in.xtrs.cloudsore.repository.DeliveryAssignmentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DeliveryService {

    private final DeliveryAssignmentRepository deliveryAssignmentRepository;
    private final OrderService orderService;
    private final UserService userService;

    public DeliveryAssignment assignDelivery(Long orderId, Long deliveryManagerId, String notes) {
        Order order = orderService.findById(orderId);
        User deliveryManager = userService.findById(deliveryManagerId);

        if (deliveryManager.getRole() != Role.DELIVERY_MANAGER) {
            throw new BusinessLogicException("User is not a delivery manager");
        }

        if (order.getStatus() != OrderStatus.READY) {
            throw new BusinessLogicException("Order must be in READY status to assign for delivery");
        }

        // Check if order is already assigned
        if (deliveryAssignmentRepository.findByOrder(order).isPresent()) {
            throw new BusinessLogicException("Order is already assigned to a delivery manager");
        }

        DeliveryAssignment assignment = new DeliveryAssignment(order, deliveryManager);
        assignment.setDeliveryNotes(notes);
        
        // Update order status
        order.setStatus(OrderStatus.OUT_FOR_DELIVERY);
        orderService.updateOrderStatus(order.getId(), OrderStatus.OUT_FOR_DELIVERY);

        return deliveryAssignmentRepository.save(assignment);
    }

    public DeliveryAssignment findById(Long id) {
        return deliveryAssignmentRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Delivery assignment not found with id: " + id));
    }

    public DeliveryAssignment findByOrder(Order order) {
        return deliveryAssignmentRepository.findByOrder(order)
                .orElseThrow(() -> new ResourceNotFoundException("No delivery assignment found for order: " + order.getOrderNumber()));
    }

    public List<DeliveryAssignment> findPendingByDeliveryManager(Long deliveryManagerId) {
        return deliveryAssignmentRepository.findPendingByDeliveryManager(deliveryManagerId);
    }

    public List<DeliveryAssignment> findCompletedByDeliveryManager(Long deliveryManagerId) {
        return deliveryAssignmentRepository.findCompletedByDeliveryManager(deliveryManagerId);
    }

    public DeliveryAssignment markAsPickedUp(Long assignmentId) {
        DeliveryAssignment assignment = findById(assignmentId);
        
        if (assignment.getPickedUpAt() != null) {
            throw new BusinessLogicException("Order is already marked as picked up");
        }

        assignment.markAsPickedUp();
        return deliveryAssignmentRepository.save(assignment);
    }

    public DeliveryAssignment markAsDelivered(Long assignmentId, String deliveryNotes, String customerFeedback, Integer rating) {
        DeliveryAssignment assignment = findById(assignmentId);
        
        if (assignment.getDeliveredAt() != null) {
            throw new BusinessLogicException("Order is already marked as delivered");
        }

        if (assignment.getPickedUpAt() == null) {
            throw new BusinessLogicException("Order must be picked up before marking as delivered");
        }

        assignment.markAsDelivered();
        assignment.setDeliveryNotes(deliveryNotes);
        assignment.setCustomerFeedback(customerFeedback);
        assignment.setDeliveryRating(rating);

        // Update order status
        Order order = assignment.getOrder();
        order.setStatus(OrderStatus.DELIVERED);
        order.setActualDeliveryTime(LocalDateTime.now());
        orderService.updateOrderStatus(order.getId(), OrderStatus.DELIVERED);

        return deliveryAssignmentRepository.save(assignment);
    }

    public DeliveryAssignment updateDeliveryNotes(Long assignmentId, String notes) {
        DeliveryAssignment assignment = findById(assignmentId);
        assignment.setDeliveryNotes(notes);
        return deliveryAssignmentRepository.save(assignment);
    }

    public List<DeliveryAssignment> getDeliveryHistory(Long deliveryManagerId, LocalDateTime startDate, LocalDateTime endDate) {
        return deliveryAssignmentRepository.findByDeliveryManagerAndDateRange(deliveryManagerId, startDate, endDate);
    }

    public Long getPendingDeliveryCount(Long deliveryManagerId) {
        return deliveryAssignmentRepository.countPendingByDeliveryManager(deliveryManagerId);
    }
}
