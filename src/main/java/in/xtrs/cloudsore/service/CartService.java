package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.*;
import in.xtrs.cloudsore.exception.BusinessLogicException;
import in.xtrs.cloudsore.exception.ResourceNotFoundException;
import in.xtrs.cloudsore.repository.CartItemRepository;
import in.xtrs.cloudsore.repository.CartRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class CartService {

    private final CartRepository cartRepository;
    private final CartItemRepository cartItemRepository;
    private final ProductService productService;

    public Cart getOrCreateCart(User user, Store store) {
        Optional<Cart> existingCart = cartRepository.findActiveCartByUserAndStore(user.getId(), store.getId());
        
        if (existingCart.isPresent()) {
            return existingCart.get();
        }
        
        Cart newCart = new Cart(user, store);
        return cartRepository.save(newCart);
    }

    public Cart getUserCart(User user, Long storeId) {
        return cartRepository.findActiveCartByUserAndStore(user.getId(), storeId)
                .orElseThrow(() -> new ResourceNotFoundException("Cart", "user and store", user.getId() + " and " + storeId));
    }

    public List<Cart> getUserCarts(User user) {
        return cartRepository.findActiveCartsByUser(user.getId());
    }

    public CartItem addToCart(User user, Store store, Long productId, Integer quantity) {
        if (quantity <= 0) {
            throw new BusinessLogicException("Quantity must be greater than 0");
        }

        Product product = productService.findProductById(productId);
        
        // Check if product belongs to the store
        if (!product.getStore().getId().equals(store.getId())) {
            throw new BusinessLogicException("Product does not belong to this store");
        }
        
        // Check stock availability
        if (product.getStockQuantity() < quantity) {
            throw new BusinessLogicException("Insufficient stock. Available: " + product.getStockQuantity());
        }

        Cart cart = getOrCreateCart(user, store);
        
        // Check if item already exists in cart
        Optional<CartItem> existingItem = cartItemRepository.findByCartAndProduct(cart, product);
        
        CartItem cartItem;
        if (existingItem.isPresent()) {
            cartItem = existingItem.get();
            int newQuantity = cartItem.getQuantity() + quantity;
            
            // Check total stock availability
            if (product.getStockQuantity() < newQuantity) {
                throw new BusinessLogicException("Insufficient stock. Available: " + product.getStockQuantity() + 
                        ", Already in cart: " + cartItem.getQuantity());
            }
            
            cartItem.updateQuantity(newQuantity);
        } else {
            cartItem = new CartItem(cart, product, quantity);
        }
        
        cartItem = cartItemRepository.save(cartItem);
        
        // Update cart totals
        updateCartTotals(cart);
        
        return cartItem;
    }

    public CartItem updateCartItem(User user, Long cartItemId, Integer quantity) {
        if (quantity <= 0) {
            throw new BusinessLogicException("Quantity must be greater than 0");
        }

        CartItem cartItem = cartItemRepository.findById(cartItemId)
                .orElseThrow(() -> new ResourceNotFoundException("CartItem", "id", cartItemId));
        
        // Verify ownership
        if (!cartItem.getCart().getUser().getId().equals(user.getId())) {
            throw new BusinessLogicException("You can only update your own cart items");
        }
        
        // Check stock availability
        if (cartItem.getProduct().getStockQuantity() < quantity) {
            throw new BusinessLogicException("Insufficient stock. Available: " + cartItem.getProduct().getStockQuantity());
        }
        
        cartItem.updateQuantity(quantity);
        cartItem = cartItemRepository.save(cartItem);
        
        // Update cart totals
        updateCartTotals(cartItem.getCart());
        
        return cartItem;
    }

    public void removeFromCart(User user, Long cartItemId) {
        CartItem cartItem = cartItemRepository.findById(cartItemId)
                .orElseThrow(() -> new ResourceNotFoundException("CartItem", "id", cartItemId));
        
        // Verify ownership
        if (!cartItem.getCart().getUser().getId().equals(user.getId())) {
            throw new BusinessLogicException("You can only remove your own cart items");
        }
        
        Cart cart = cartItem.getCart();
        cartItemRepository.delete(cartItem);
        
        // Update cart totals
        updateCartTotals(cart);
    }

    public void clearCart(User user, Long storeId) {
        Optional<Cart> cart = cartRepository.findActiveCartByUserAndStore(user.getId(), storeId);
        if (cart.isPresent()) {
            cartItemRepository.deleteByCart(cart.get());
            updateCartTotals(cart.get());
        }
    }

    public List<CartItem> getCartItems(User user, Long storeId) {
        Cart cart = getUserCart(user, storeId);
        return cartItemRepository.findByCart(cart);
    }

    private void updateCartTotals(Cart cart) {
        List<CartItem> items = cartItemRepository.findByCart(cart);
        
        BigDecimal totalAmount = items.stream()
                .map(CartItem::getTotalPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        Integer totalItems = items.stream()
                .mapToInt(CartItem::getQuantity)
                .sum();
        
        cart.setTotalAmount(totalAmount);
        cart.setTotalItems(totalItems);
        cartRepository.save(cart);
    }

    public Cart findById(Long id) {
        return cartRepository.findById(id)
                .orElseThrow(() -> new ResourceNotFoundException("Cart", "id", id));
    }

    public void deactivateCart(Cart cart) {
        cart.setIsActive(false);
        cartRepository.save(cart);
    }
}
