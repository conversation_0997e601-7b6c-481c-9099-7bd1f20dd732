package in.xtrs.cloudsore.service;

import in.xtrs.cloudsore.entity.DeliveryAssignment;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.OrderStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class DeliveryAssignmentService {

    private final DeliveryService deliveryService;

    public List<DeliveryAssignment> getAssignmentsByDeliveryManager(User deliveryManager, OrderStatus status) {
        if (status == OrderStatus.DELIVERED) {
            return deliveryService.findCompletedByDeliveryManager(deliveryManager.getId());
        } else {
            return deliveryService.findPendingByDeliveryManager(deliveryManager.getId());
        }
    }

    public List<DeliveryAssignment> getAllAssignments(OrderStatus status) {
        // This would need to be implemented in DeliveryService
        // For now, return empty list or implement in DeliveryService
        return List.of();
    }

    public void updateDeliveryStatus(Long orderId, OrderStatus status, String notes, User currentUser) {
        // Find assignment by order and update status
        // This would need to be implemented based on the order ID
        // For now, we'll use the existing methods
    }

    public DeliveryAssignment getAssignmentByOrderAndDeliveryManager(Long orderId, Long deliveryManagerId) {
        // This would need to be implemented in DeliveryService
        return null;
    }

    public void markAsPickedUp(Long orderId, String notes) {
        // This would need to find assignment by order ID first
        // For now, we'll need to implement this in DeliveryService
    }

    public void markAsDelivered(Long orderId, String deliveryNotes, String customerFeedback, Integer deliveryRating) {
        // This would need to find assignment by order ID first
        // For now, we'll need to implement this in DeliveryService
    }

    public List<DeliveryAssignment> getDeliveryHistory(User deliveryManager, int page, int size) {
        // This would need pagination support in DeliveryService
        return deliveryService.findCompletedByDeliveryManager(deliveryManager.getId());
    }

    public List<DeliveryAssignment> getAllDeliveryHistory(int page, int size) {
        // This would need to be implemented in DeliveryService
        return List.of();
    }

    public DeliveryAssignment assignOrder(Long orderId, Long deliveryManagerId, User currentUser) {
        return deliveryService.assignDelivery(orderId, deliveryManagerId, "Assigned by " + currentUser.getName());
    }
}
