package in.xtrs.cloudsore.dto;

import in.xtrs.cloudsore.enums.TaxType;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class CreateTaxRequest {
    
    @NotBlank(message = "Tax name is required")
    private String name;
    
    @NotNull(message = "Tax type is required")
    private TaxType taxType;
    
    @NotNull(message = "Tax rate is required")
    @DecimalMin(value = "0.0", message = "Tax rate must be at least 0")
    @DecimalMax(value = "100.0", message = "Tax rate must not exceed 100")
    private BigDecimal taxRate;
    
    private String description;
}
