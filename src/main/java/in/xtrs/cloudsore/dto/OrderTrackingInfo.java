package in.xtrs.cloudsore.dto;

import in.xtrs.cloudsore.enums.OrderStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderTrackingInfo {
    
    private Long orderId;
    private String orderNumber;
    private OrderStatus status;
    private String trackingNumber;
    private LocalDateTime estimatedDeliveryTime;
    private LocalDateTime actualDeliveryTime;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Delivery information
    private String deliveryManagerName;
    private String deliveryManagerPhone;
    private LocalDateTime pickedUpAt;
    private LocalDateTime deliveredAt;
    private String deliveryNotes;
}
