package in.xtrs.cloudsore.dto;

import in.xtrs.cloudsore.enums.OrderStatus;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateDeliveryStatusRequest {
    
    @NotNull(message = "Order status is required")
    private OrderStatus status;
    
    private String deliveryNotes;
    
    private String customerFeedback;
    
    private Integer deliveryRating; // 1-5 stars
}
