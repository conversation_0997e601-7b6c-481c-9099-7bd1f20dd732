package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.DeliverySlot;
import in.xtrs.cloudsore.entity.Store;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DeliverySlotRepository extends JpaRepository<DeliverySlot, Long> {
    
    List<DeliverySlot> findByStore(Store store);
    
    List<DeliverySlot> findByStoreAndDeliveryDate(Store store, LocalDate deliveryDate);
    
    List<DeliverySlot> findByStoreAndIsActive(Store store, Boolean isActive);
    
    @Query("SELECT ds FROM DeliverySlot ds WHERE ds.store.id = :storeId AND ds.deliveryDate = :date AND ds.isActive = true AND ds.isAvailable = true")
    List<DeliverySlot> findAvailableByStoreAndDate(@Param("storeId") Long storeId, @Param("date") LocalDate date);
    
    @Query("SELECT ds FROM DeliverySlot ds WHERE ds.store.id = :storeId AND ds.deliveryDate >= :startDate AND ds.deliveryDate <= :endDate AND ds.isActive = true")
    List<DeliverySlot> findByStoreAndDateRange(@Param("storeId") Long storeId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
    
    @Query("SELECT ds FROM DeliverySlot ds WHERE ds.store.id = :storeId AND ds.deliveryDate >= :date AND ds.isActive = true AND ds.isAvailable = true AND ds.currentOrders < ds.maxOrders ORDER BY ds.deliveryDate, ds.startTime")
    List<DeliverySlot> findBookableSlotsByStoreFromDate(@Param("storeId") Long storeId, @Param("date") LocalDate date);
    
    @Query("SELECT ds FROM DeliverySlot ds WHERE ds.deliveryDate < :date")
    List<DeliverySlot> findExpiredSlots(@Param("date") LocalDate date);
}
