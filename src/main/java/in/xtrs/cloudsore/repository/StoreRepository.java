package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StoreRepository extends JpaRepository<Store, Long> {
    
    Optional<Store> findByAdmin(User admin);
    
    List<Store> findByIsActive(Boolean isActive);
    
    @Query("SELECT s FROM Store s WHERE s.isActive = true")
    List<Store> findAllActiveStores();
    
    @Query("SELECT s FROM Store s WHERE s.name LIKE %:name% AND s.isActive = true")
    List<Store> findByNameContainingAndIsActive(@Param("name") String name);
    
    boolean existsByAdmin(User admin);

    // Count methods for analytics
    Long countByIsActive(Boolean isActive);
}
