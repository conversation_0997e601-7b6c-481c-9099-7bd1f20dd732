package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.Product;
import in.xtrs.cloudsore.entity.ProductTax;
import in.xtrs.cloudsore.entity.Tax;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ProductTaxRepository extends JpaRepository<ProductTax, Long> {
    
    List<ProductTax> findByProduct(Product product);
    
    List<ProductTax> findByTax(Tax tax);
    
    List<ProductTax> findByProductAndIsActive(Product product, Boolean isActive);
    
    @Query("SELECT pt FROM ProductTax pt WHERE pt.product.id = :productId AND pt.isActive = true")
    List<ProductTax> findActiveByProductId(@Param("productId") Long productId);
    
    @Query("SELECT pt FROM ProductTax pt WHERE pt.tax.id = :taxId AND pt.isActive = true")
    List<ProductTax> findActiveByTaxId(@Param("taxId") Long taxId);
    
    boolean existsByProductAndTax(Product product, Tax tax);
}
