package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.DeliveryAssignment;
import in.xtrs.cloudsore.entity.Order;
import in.xtrs.cloudsore.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface DeliveryAssignmentRepository extends JpaRepository<DeliveryAssignment, Long> {
    
    Optional<DeliveryAssignment> findByOrder(Order order);
    
    List<DeliveryAssignment> findByDeliveryManager(User deliveryManager);
    
    @Query("SELECT da FROM DeliveryAssignment da WHERE da.deliveryManager.id = :deliveryManagerId AND da.deliveredAt IS NULL ORDER BY da.assignedAt")
    List<DeliveryAssignment> findPendingByDeliveryManager(@Param("deliveryManagerId") Long deliveryManagerId);
    
    @Query("SELECT da FROM DeliveryAssignment da WHERE da.deliveryManager.id = :deliveryManagerId AND da.deliveredAt IS NOT NULL ORDER BY da.deliveredAt DESC")
    List<DeliveryAssignment> findCompletedByDeliveryManager(@Param("deliveryManagerId") Long deliveryManagerId);
    
    @Query("SELECT da FROM DeliveryAssignment da WHERE da.assignedAt BETWEEN :startDate AND :endDate")
    List<DeliveryAssignment> findByAssignedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT da FROM DeliveryAssignment da WHERE da.deliveryManager.id = :deliveryManagerId AND da.assignedAt BETWEEN :startDate AND :endDate")
    List<DeliveryAssignment> findByDeliveryManagerAndDateRange(@Param("deliveryManagerId") Long deliveryManagerId, @Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(da) FROM DeliveryAssignment da WHERE da.deliveryManager.id = :deliveryManagerId AND da.deliveredAt IS NULL")
    Long countPendingByDeliveryManager(@Param("deliveryManagerId") Long deliveryManagerId);
}
