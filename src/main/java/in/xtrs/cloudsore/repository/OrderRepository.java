package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.Order;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import in.xtrs.cloudsore.enums.OrderStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {
    
    Optional<Order> findByOrderNumber(String orderNumber);
    
    List<Order> findByUser(User user);
    
    List<Order> findByStore(Store store);
    
    List<Order> findByStatus(OrderStatus status);
    
    List<Order> findByUserAndStatus(User user, OrderStatus status);
    
    List<Order> findByStoreAndStatus(Store store, OrderStatus status);
    
    @Query("SELECT o FROM Order o WHERE o.user.id = :userId ORDER BY o.createdAt DESC")
    List<Order> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId);
    
    @Query("SELECT o FROM Order o WHERE o.store.id = :storeId ORDER BY o.createdAt DESC")
    List<Order> findByStoreIdOrderByCreatedAtDesc(@Param("storeId") Long storeId);
    
    @Query("SELECT o FROM Order o WHERE o.createdAt BETWEEN :startDate AND :endDate")
    List<Order> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT COUNT(o) FROM Order o WHERE o.store.id = :storeId AND o.status = :status")
    Long countByStoreIdAndStatus(@Param("storeId") Long storeId, @Param("status") OrderStatus status);

    // Additional methods for enhanced functionality
    List<Order> findByUserAndStoreIdAndStatusOrderByCreatedAtDesc(User user, Long storeId, OrderStatus status);

    List<Order> findByUserAndStoreIdOrderByCreatedAtDesc(User user, Long storeId);

    List<Order> findByUserAndStatusOrderByCreatedAtDesc(User user, OrderStatus status);

    List<Order> findByStoreIdAndStatusOrderByCreatedAtDesc(Long storeId, OrderStatus status);

    List<Order> findByStatusOrderByCreatedAtDesc(OrderStatus status);

    List<Order> findAllByOrderByCreatedAtDesc();

    @Query("SELECT o FROM Order o WHERE o.store.id = :storeId ORDER BY o.createdAt DESC LIMIT :limit")
    List<Order> findTopByStoreIdOrderByCreatedAtDesc(@Param("storeId") Long storeId, @Param("limit") int limit);

    // Count methods for analytics
    Long countByStoreId(Long storeId);

    Long countByStoreIdAndCreatedAtBetween(Long storeId, LocalDateTime startDate, LocalDateTime endDate);

    Long countByCreatedAtBetween(LocalDateTime startDate, LocalDateTime endDate);

    Long countByStatus(OrderStatus status);
}
