package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.Cart;
import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CartRepository extends JpaRepository<Cart, Long> {
    
    Optional<Cart> findByUserAndStoreAndIsActive(User user, Store store, Boolean isActive);
    
    Optional<Cart> findByUser(User user);
    
    List<Cart> findByUserAndIsActive(User user, Boolean isActive);
    
    List<Cart> findByStore(Store store);
    
    List<Cart> findByStoreAndIsActive(Store store, Boolean isActive);
    
    @Query("SELECT c FROM Cart c WHERE c.user.id = :userId AND c.store.id = :storeId AND c.isActive = true")
    Optional<Cart> findActiveCartByUserAndStore(@Param("userId") Long userId, @Param("storeId") Long storeId);
    
    @Query("SELECT c FROM Cart c WHERE c.user.id = :userId AND c.isActive = true")
    List<Cart> findActiveCartsByUser(@Param("userId") Long userId);
    
    @Query("SELECT COUNT(c) FROM Cart c WHERE c.store.id = :storeId AND c.isActive = true")
    Long countActiveCartsByStore(@Param("storeId") Long storeId);
    
    void deleteByUserAndStore(User user, Store store);
}
