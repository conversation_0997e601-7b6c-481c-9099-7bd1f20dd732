package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.Category;
import in.xtrs.cloudsore.entity.Store;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CategoryRepository extends JpaRepository<Category, Long> {
    
    List<Category> findByStore(Store store);
    
    List<Category> findByStoreAndIsActive(Store store, Boolean isActive);
    
    @Query("SELECT c FROM Category c WHERE c.store.id = :storeId AND c.isActive = true")
    List<Category> findActiveByStoreId(@Param("storeId") Long storeId);
    
    @Query("SELECT c FROM Category c WHERE c.name LIKE %:name% AND c.store = :store AND c.isActive = true")
    List<Category> findByNameContainingAndStoreAndIsActive(@Param("name") String name, @Param("store") Store store);
    
    boolean existsByNameAndStore(String name, Store store);

    // Count methods for analytics
    Long countByStoreId(Long storeId);

    Long countByStoreIdAndIsActive(Long storeId, Boolean isActive);
}
