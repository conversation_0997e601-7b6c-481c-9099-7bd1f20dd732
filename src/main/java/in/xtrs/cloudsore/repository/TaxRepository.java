package in.xtrs.cloudsore.repository;

import in.xtrs.cloudsore.entity.Store;
import in.xtrs.cloudsore.entity.Tax;
import in.xtrs.cloudsore.enums.TaxType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TaxRepository extends JpaRepository<Tax, Long> {
    
    List<Tax> findByStore(Store store);
    
    List<Tax> findByStoreAndIsActive(Store store, Boolean isActive);
    
    List<Tax> findByTaxType(TaxType taxType);
    
    List<Tax> findByStoreAndTaxType(Store store, TaxType taxType);
    
    @Query("SELECT t FROM Tax t WHERE t.store.id = :storeId AND t.isActive = true")
    List<Tax> findActiveByStoreId(@Param("storeId") Long storeId);
    
    @Query("SELECT t FROM Tax t WHERE t.store.id = :storeId AND t.taxType = :taxType AND t.isActive = true")
    List<Tax> findActiveByStoreIdAndTaxType(@Param("storeId") Long storeId, @Param("taxType") TaxType taxType);
    
    boolean existsByNameAndStore(String name, Store store);
}
