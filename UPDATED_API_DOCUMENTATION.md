# **CloudStore API Documentation - Updated Role Structure**

## **🔐 Authentication APIs**
**Base Path:** `/api/auth`

| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `POST` | `/register/phone` | Register with phone number and send OTP | `AuthRequest` | `AuthResponse` |
| `POST` | `/login/phone` | Send OTP to existing user's phone | `PhoneLoginRequest` | `AuthResponse` |
| `POST` | `/verify/otp` | Verify OTP and get JWT tokens | `OtpRequest` | `TokenResponse` |
| `POST` | `/refresh` | Refresh access token using refresh token | `RefreshTokenRequest` | `TokenResponse` |
| `POST` | `/validate` | Validate JWT token | Header: `Authorization` | `AuthResponse` |
| `GET` | `/me` | Get current authenticated user info | Header: `Authorization` | `User` |

---

## **🌐 Public APIs (No Authentication)**
**Base Path:** `/api/public`

| Method | Endpoint | Description | Response |
|--------|----------|-------------|----------|
| `GET` | `/stores` | Get all active stores | `List<Store>` |
| `GET` | `/stores/{storeId}/products` | Get public products for a store | `List<Product>` |
| `GET` | `/health` | Health check endpoint | `String` |

---

## **👑 SUPER ADMIN APIs**
**Base Path:** `/api/superadmin`
**Required Role:** `SUPERADMIN`

### **User Management**
| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `GET` | `/users` | Get all users with optional role filtering | Query: `role` | `List<User>` |
| `PUT` | `/users/{userId}/role` | Update user role | `UpdateUserRoleRequest` | `User` |
| `PUT` | `/users/{userId}/status` | Activate/deactivate user | `UpdateUserStatusRequest` | `User` |
| `POST` | `/store-managers` | Create store manager | `CreateStoreManagerRequest` | `User` |
| `POST` | `/delivery-managers` | Create delivery manager | `CreateDeliveryManagerRequest` | `User` |

### **Store Management**
| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `POST` | `/stores/create` | Create new store | `CreateStoreRequest` | `Store` |
| `GET` | `/stores` | Get all stores | - | `List<Store>` |
| `PUT` | `/stores/{storeId}/status` | Activate/deactivate store | `UpdateStoreStatusRequest` | `Store` |

### **Order Management**
| Method | Endpoint | Description | Query Params | Response |
|--------|----------|-------------|--------------|----------|
| `GET` | `/orders` | Get all orders with filtering | `storeId`, `status`, `page`, `size` | `List<Order>` |

### **Analytics**
| Method | Endpoint | Description | Response |
|--------|----------|-------------|----------|
| `GET` | `/analytics` | Get system-wide analytics | `AnalyticsResponse` |

---

## **🏪 STORE MANAGER APIs**
**Base Path:** `/api/store-manager`
**Required Role:** `STORE_MANAGER`

### **Store Management**
| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `GET` | `/my-store` | Get managed store details | - | `Store` |
| `PUT` | `/store-info` | Update store information | `UpdateStoreInfoRequest` | `Store` |

### **Order Management**
| Method | Endpoint | Description | Query Params | Response |
|--------|----------|-------------|--------------|----------|
| `GET` | `/orders` | Get store orders | `status`, `page`, `size` | `List<Order>` |
| `GET` | `/recent-orders` | Get recent orders | `limit` | `List<Order>` |
| `GET` | `/orders-ready-for-delivery` | Get orders ready for delivery | - | `List<Order>` |
| `PUT` | `/orders/{orderId}/status` | Update order status | `UpdateOrderStatusRequest` | `Order` |

### **Product & Category Management**
**Base Path:** `/api/stores/{storeId}`

| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `POST` | `/categories` | Create category | `CreateCategoryRequest` | `Category` |
| `PUT` | `/categories/{categoryId}` | Update category | `UpdateCategoryRequest` | `Category` |
| `DELETE` | `/categories/{categoryId}` | Delete category | - | `void` |
| `POST` | `/products` | Create product | `CreateProductRequest` | `Product` |
| `PUT` | `/products/{productId}` | Update product | `UpdateProductRequest` | `Product` |
| `PUT` | `/products/{productId}/featured` | Toggle product featured status | - | `Product` |
| `DELETE` | `/products/{productId}` | Delete product | - | `void` |

### **Delivery Management**
| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `POST` | `/delivery-slots` | Create delivery slot | `CreateDeliverySlotRequest` | `DeliverySlot` |
| `POST` | `/assign-delivery` | Assign order to delivery manager | `AssignDeliveryRequest` | `DeliveryAssignment` |

---

## **🚚 DELIVERY MANAGER APIs**
**Base Path:** `/api/delivery`
**Required Role:** `DELIVERY_MANAGER`

### **Delivery Management**
| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `GET` | `/assignments` | Get assigned deliveries | Query: `status` | `List<DeliveryAssignment>` |
| `PUT` | `/{assignmentId}/pickup` | Mark order as picked up | - | `DeliveryAssignment` |
| `PUT` | `/{assignmentId}/deliver` | Mark order as delivered | `UpdateDeliveryStatusRequest` | `DeliveryAssignment` |
| `PUT` | `/{assignmentId}/notes` | Update delivery notes | `UpdateDeliveryNotesRequest` | `DeliveryAssignment` |
| `GET` | `/history` | Get delivery history | Query: `startDate`, `endDate` | `List<DeliveryAssignment>` |
| `GET` | `/pending-count` | Get pending delivery count | - | `Long` |

---

## **👤 USER APIs**
**Base Path:** `/api/user`
**Required Role:** `USER` (and higher roles)

### **Profile Management**
| Method | Endpoint | Description | Response |
|--------|----------|-------------|----------|
| `GET` | `/profile` | Get user profile | `User` |

### **Cart Management**
**Base Path:** `/api/cart`

| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `POST` | `/add` | Add item to cart | `AddToCartRequest` | `CartItem` |
| `GET` | `/store/{storeId}` | Get cart items for store | - | `List<CartItem>` |
| `GET` | `/all` | Get all user carts | - | `List<Cart>` |
| `PUT` | `/update/{cartItemId}` | Update cart item quantity | `UpdateCartItemRequest` | `CartItem` |
| `DELETE` | `/remove/{cartItemId}` | Remove item from cart | - | `void` |
| `DELETE` | `/clear/{storeId}` | Clear cart for store | - | `void` |

### **Order Management**
**Base Path:** `/api/user/orders`

| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `POST` | `/create-from-cart` | Create order from cart | `CreateOrderFromCartRequest` | `Order` |
| `GET` | `/my-orders` | Get user's order history | Query: `status`, `page`, `size` | `List<Order>` |
| `GET` | `/{orderId}` | Get specific order details | - | `Order` |
| `PUT` | `/{orderId}/cancel` | Cancel order | - | `Order` |

---

## **🏬 Store & Product APIs**
**Base Path:** `/api/stores`
**Access:** Various roles based on endpoint

### **Store Operations**
| Method | Endpoint | Description | Required Role | Request Body | Response |
|--------|----------|-------------|---------------|--------------|----------|
| `GET` | `/` | Get all active stores | All authenticated | - | `List<Store>` |
| `GET` | `/{id}` | Get store by ID | All authenticated | - | `Store` |
| `PUT` | `/{id}` | Update store | `SUPERADMIN` or Store Manager | `UpdateStoreRequest` | `Store` |
| `DELETE` | `/{id}` | Delete store | `SUPERADMIN` | - | `void` |
| `GET` | `/my-store` | Get store manager's store | `STORE_MANAGER` | - | `Store` |

### **Product & Category Operations**
**Base Path:** `/api/stores/{storeId}`

| Method | Endpoint | Description | Required Role | Request Body | Response |
|--------|----------|-------------|---------------|--------------|----------|
| `GET` | `/categories` | Get store categories | All authenticated | - | `List<Category>` |
| `GET` | `/products` | Get store products | All authenticated | Query: `categoryId` | `List<Product>` |
| `GET` | `/products/{productId}` | Get product details | All authenticated | - | `Product` |
| `GET` | `/delivery-slots` | Get available delivery slots | All authenticated | Query: `date` | `List<DeliverySlot>` |

---

## **📋 Order Management APIs**
**Base Path:** `/api/orders`
**Required Role:** `SUPERADMIN`, `STORE_MANAGER`

| Method | Endpoint | Description | Request Body | Response |
|--------|----------|-------------|--------------|----------|
| `POST` | `/` | Create new order | `CreateOrderRequest` | `Order` |
| `GET` | `/` | Get orders (filtered by role) | Query: `storeId`, `status` | `List<Order>` |
| `GET` | `/{id}` | Get order by ID | - | `Order` |
| `PUT` | `/{id}/status` | Update order status | `UpdateOrderStatusRequest` | `Order` |
| `DELETE` | `/{id}` | Cancel order | - | `void` |

---

## **🔒 Updated Role Structure**

### **3-Role System:**
1. **SUPERADMIN**: Creates stores, assigns store managers, full system access
2. **STORE_MANAGER**: Manages assigned store (products, categories, orders, delivery assignments)
3. **DELIVERY_MANAGER**: Handles delivery assignments and status updates
4. **USER**: Orders products and tracks delivery

### **Key Changes:**
- **ADMIN role removed** - merged with STORE_MANAGER
- All previous ADMIN permissions now belong to STORE_MANAGER
- Store managers have full control over their assigned store
- Consistent role naming throughout the application

### **Security & Access Control:**
- **JWT Authentication**: All protected endpoints require `Authorization: Bearer <token>` header
- **Role-based Access**: Each endpoint specifies required roles
- **Store-level Authorization**: Store managers can only access their assigned store data
- **Delivery Manager Authorization**: Can only access assigned deliveries

### **📱 Swagger UI Access**
- **Swagger UI**: `http://localhost:8080/swagger-ui.html`
- **OpenAPI JSON**: `http://localhost:8080/api-docs`

The APIs are organized into groups in Swagger:
- **Public APIs**: No authentication required
- **Authentication APIs**: Login, registration, token management
- **Admin APIs**: Store and order management (SUPERADMIN/STORE_MANAGER)
- **User APIs**: User profile and cart management

This updated structure provides a cleaner 3-role system with OTP-only authentication and JWT token management.
