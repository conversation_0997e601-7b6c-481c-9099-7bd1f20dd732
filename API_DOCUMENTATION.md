# Cloud Store API Documentation

## Overview
This document provides a comprehensive list of all APIs available for each role in the Cloud Store application.

## Authentication
All APIs (except public endpoints) require JWT authentication via the `Authorization` header:
```
Authorization: Bearer <jwt-token>
```

## Roles and Permissions

### 1. SUPERADMIN
**Description**: System administrator with full access to all features.

#### User Management
- `GET /api/superadmin/users` - Get all users with optional role filtering
- `PUT /api/superadmin/users/{userId}/role` - Update user role
- `PUT /api/superadmin/users/{userId}/status` - Activate/deactivate user
- `POST /api/superadmin/store-managers` - Create store manager
- `POST /api/superadmin/delivery-managers` - Create delivery manager

#### Store Management
- `POST /api/stores/create` - Create new store
- `GET /api/superadmin/stores` - Get all stores
- `PUT /api/superadmin/stores/{storeId}/status` - Activate/deactivate store

#### Order Management
- `GET /api/superadmin/orders` - Get all orders with filtering
- `GET /api/orders` - Get orders with filters
- `POST /api/orders` - Create order
- `PUT /api/orders/{orderId}/status` - Update order status
- `DELETE /api/orders/{orderId}` - Cancel order

#### Analytics
- `GET /api/superadmin/analytics` - Get system-wide analytics

### 2. STORE_MANAGER (formerly ADMIN)
**Description**: Manages a specific store including products, categories, orders, and delivery assignments.

#### Store Management
- `GET /api/store-manager/my-store` - Get managed store details
- `PUT /api/store-manager/store-info` - Update store information

#### Product & Category Management
- `POST /api/stores/{storeId}/categories` - Create category
- `GET /api/stores/{storeId}/categories` - Get store categories
- `PUT /api/stores/{storeId}/categories/{categoryId}` - Update category
- `DELETE /api/stores/{storeId}/categories/{categoryId}` - Delete category
- `POST /api/stores/{storeId}/products` - Create product
- `GET /api/stores/{storeId}/products` - Get store products
- `PUT /api/stores/{storeId}/products/{productId}` - Update product
- `DELETE /api/stores/{storeId}/products/{productId}` - Delete product

#### Order Management
- `GET /api/store-manager/orders` - Get store orders
- `PUT /api/store-manager/orders/{orderId}/status` - Update order status
- `GET /api/store-manager/recent-orders` - Get recent orders
- `GET /api/store-manager/orders-ready-for-delivery` - Get orders ready for delivery

#### Tax Management
- `POST /api/stores/{storeId}/taxes` - Create tax
- `GET /api/stores/{storeId}/taxes` - Get store taxes
- `PUT /api/stores/{storeId}/taxes/{taxId}` - Update tax
- `DELETE /api/stores/{storeId}/taxes/{taxId}` - Delete tax

#### Delivery Management
- `POST /api/stores/{storeId}/delivery-slots` - Create delivery slot
- `GET /api/stores/{storeId}/delivery-slots` - Get delivery slots
- `PUT /api/stores/{storeId}/delivery-slots/{slotId}` - Update delivery slot
- `DELETE /api/stores/{storeId}/delivery-slots/{slotId}` - Delete delivery slot
- `POST /api/delivery/assign` - Assign order to delivery manager
- `GET /api/store-manager/delivery-managers` - Get available delivery managers

#### Analytics
- `GET /api/store-manager/analytics` - Get store analytics
- `GET /api/store-manager/low-stock-products` - Get low stock products

### 3. DELIVERY_MANAGER
**Description**: Handles order deliveries and updates delivery status.

#### Delivery Management
- `GET /api/delivery/my-pending` - Get pending deliveries
- `GET /api/delivery/my-completed` - Get completed deliveries
- `PUT /api/delivery/{assignmentId}/pickup` - Mark order as picked up
- `PUT /api/delivery/{assignmentId}/deliver` - Mark order as delivered
- `PUT /api/delivery/{assignmentId}/notes` - Update delivery notes
- `GET /api/delivery/history` - Get delivery history
- `GET /api/delivery/pending-count` - Get pending delivery count

### 4. USER
**Description**: Regular customers who can browse products, manage cart, and place orders.

#### Cart Management
- `POST /api/cart/add` - Add item to cart
- `GET /api/cart/store/{storeId}` - Get cart items for store
- `GET /api/cart/all` - Get all user carts
- `PUT /api/cart/item/{cartItemId}` - Update cart item quantity
- `DELETE /api/cart/item/{cartItemId}` - Remove item from cart
- `DELETE /api/cart/store/{storeId}/clear` - Clear cart for store

#### Order Management
- `POST /api/user/orders/create-from-cart` - Create order from cart
- `GET /api/user/orders/history` - Get order history
- `GET /api/user/orders/{orderId}` - Get order details
- `GET /api/user/orders/{orderId}/track` - Track order
- `PUT /api/user/orders/{orderId}/cancel` - Cancel order

#### Product Browsing
- `GET /api/stores` - Get all stores
- `GET /api/stores/{storeId}/categories` - Get store categories
- `GET /api/stores/{storeId}/products` - Get store products
- `GET /api/stores/{storeId}/delivery-slots` - Get available delivery slots

#### User Profile
- `GET /api/users/profile` - Get user profile

## Public APIs (No Authentication Required)

### Store & Product Browsing
- `GET /api/public/stores` - Get public stores
- `GET /api/public/stores/{storeId}/products` - Get public products

### Authentication
- `POST /api/auth/register` - Register with phone number
- `POST /api/auth/verify-otp` - Verify OTP
- `POST /api/auth/login` - Login with phone/OTP
- `POST /api/auth/refresh` - Refresh access token
- `POST /api/auth/logout` - Logout

## Common Response Format

### Success Response
```json
{
  "success": true,
  "message": "Operation successful",
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## Order Status Flow
1. `PENDING` - Order created
2. `CONFIRMED` - Order confirmed by store
3. `PREPARING` - Order being prepared
4. `READY` - Order ready for delivery
5. `OUT_FOR_DELIVERY` - Order assigned to delivery manager
6. `DELIVERED` - Order delivered to customer
7. `CANCELLED` - Order cancelled

## Role Hierarchy (Updated)
- **SUPERADMIN**: Full system access, creates stores and assigns store managers
- **STORE_MANAGER**: Store-specific management (formerly ADMIN role)
- **DELIVERY_MANAGER**: Delivery operations and status updates
- **USER**: Customer operations (browse, cart, orders)
- **GUEST**: Public access only

**Note**: The ADMIN role has been merged with STORE_MANAGER for consistency.
